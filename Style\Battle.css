#BattleScreen {
    position: absolute;
   
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    width: 100%;
    height: 100%;
    display: none;
    z-index: 1;
    position: relative;
}

#playerbattleinfo {
    position: absolute;
    top: 0;
    right: 0;
    width: 35%;
    height: 20%;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: end;
    justify-content: center;
    gap: 20px;
    opacity: 0;
    animation: righttoleftappear 0.5s linear forwards;
    animation-delay: 0.1s;
}

#playername {
    width: 100%;
    height: 20%;
    font-size: 35px;
    color: white;
    font-weight: bold;
    justify-items: flex-end;
    text-align: right;
    padding-right: 20px;
    letter-spacing: 2px;
}

#playerbattlehpbar {
    width: 500px;
    height: 18px;
    background-color: rgb(156, 16, 0);
    border-left: 5px solid rgb(255, 156, 8);
    border-bottom: 4px solid rgb(255, 156, 8);
    border-right: 5px solid rgb(57, 0, 0);
    border-top: 4px solid rgb(57, 0, 0);
    transform: scaleX(-1);
    z-index: 0 !important;
}

#playerbattlehpbar_inner {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 0, 0, 0.8);
    z-index: 1 !important;
    transition: all 1.5s ease;
}

#playerbattlehp-inner {
    width: 500px;
    height: 100%;
    background: rgb(255, 255, 90);
    border-right: 2px solid rgb(156, 16, 0);
    box-shadow: 0 0 10px 0px rgba(0, 0, 0, .5);
    transition: all 1.5s ease;
    transform: scaleX(-1);
    transition: all 0.3s ease;
    z-index: 99 !important;
}

#playerbattlehptext {
    width: 100%;
    height: 20%;
    font-size: 20px;
    color: white;
    font-weight: bold;
    text-align: right;
    padding-right: 20px;
    letter-spacing: 2px;
}

@keyframes righttoleftappear {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0%);
        opacity: 1;
    }
}

/*敵人資訊*/
#enemybattleinfo {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 35%;
    height: 20%;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: center;
    gap: 20px;
    opacity: 0;
    animation: lefttorightappear 0.5s linear forwards;
    animation-delay: 0.1s;
}

#enemyname {
    width: 100%;
    height: 20%;
    font-size: 35px;
    color: white;
    font-weight: bold;
    justify-items: flex-start;
    text-align: left;
    padding-left: 20px;
    letter-spacing: 2px;
}

#enemybattlehpbar {
    width: 500px;
    height: 18px;
    background-color: rgb(156, 16, 0);
    border-left: 5px solid rgb(255, 156, 8);
    border-bottom: 4px solid rgb(255, 156, 8);
    border-right: 5px solid rgb(57, 0, 0);
    border-top: 4px solid rgb(57, 0, 0);
    z-index: 0 !important;
}

#enemybattlehpbar_inner {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 0, 0, 0.8);
    z-index: 1 !important;
    transition: all 1.5s ease;
}

#enemybattlehp-inner {
    width: 500px;
    height: 100%;
    background: rgb(255, 255, 90);
    border-right: 2px solid rgb(156, 16, 0);
    box-shadow: 0 0 10px 0px rgba(0, 0, 0, .5);
    transition: all 0.3s ease;
    z-index: 99 !important;
}

#enemybattlehptext {
    width: 100%;
    height: 20%;
    font-size: 20px;
    color: white;
    font-weight: bold;
    text-align: left;
    padding-left: 20px;
    letter-spacing: 2px;
}

#sysmsgid {
    position: absolute;
    top: 8%;
    left: 0;
    width: 95%;
    height: 100%;
    z-index: 99;
    display: flex;
    align-items: end;
    justify-content: center;
    font-size: 30px;
    color: white;
    font-weight: bold;
    background: none;
    outline: none;
    border: none;
}

#sysmsgid::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

.sysmsg-avatar {
    width: 300px;
    height: 300px;
    position: absolute;
    bottom: 0;
    left: 1%;
    z-index: 99;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    animation: playerdamageappear 0.5s linear forwards;
}

.sysmsg-container {
    width: 100%;
    height: 28%;
    background-image: url('../Public/msg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    animation: playerdamageappear 0.5s linear forwards;
    display: flex;
    align-items: center;
    justify-content: end;
}

.sysmsg-textcontent {
    width: 75%;
    height: 90%;
    display: flex;
    align-items: start;
    justify-content: start;
    font-size: 30px;
    color: rgb(168, 105, 38);
    font-weight: bold;
    margin-right: 20px;
}


#lvupmsgid {
    position: absolute;
    top: 0%;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    color: white;
    font-weight: bold;
    background: none;
    outline: none;
    border: none;
}

#lvupmsgid::backdrop {
    background-color: rgba(0, 0, 0, .5);
}



.LVup-container {
    width: 24%;
    height: 75%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    animation: playerdamageappear 0.5s linear forwards;
    color: rgb(168, 105, 38);
    font-size: 25px;

}

.LVup-ability {
    width: 95%;
    height: 80%;
    background: rgb(247, 231, 173);
    border: 6px solid rgb(165, 90, 24);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 5px;
}

.LVup-ability-item {
    width: 90%;
    height: 12%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
}

.LVup-skills {
    width: 95%;
    height: 15%;
    background: rgb(247, 231, 173);
    border: 6px solid rgb(165, 90, 24);
    box-shadow: 0 0 5px black;
    border-radius: 5px;
    font-size: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}

















@keyframes lefttorightappear {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }

    to {
        transform: translateX(0%);
        opacity: 1;
    }
}


@keyframes playerdamageappear {
    from {
        transform: translateY(100%);
        opacity: 0;
    }

    to {
        transform: translateY(0%);
        opacity: 1;
    }
}

@keyframes playerdamagedisappear {
    from {
        transform: translateY(0%);
        opacity: 1;
    }

    to {
        transform: translateY(-100%);
        opacity: 0;
    }
}

/* 戰鬥畫面(原點到左上)震動*/

@keyframes shakeOtoLU {
    0% {
        transform: translate(0px, 0px);
    }

    50% {
        transform: translate(-20px, -20px);
    }

    100% {
        transform: translate(0px, 0px);
    }
}

/* 戰鬥畫面(原點到右上)震動*/

@keyframes shakeOtoRU {
    0% {
        transform: translate(0px, 0px);
    }

    50% {
        transform: translate(30px, -30px);
    }

    100% {
        transform: translate(0px, 0px);
    }
}


@keyframes criticalhit {
    0% {
        filter: brightness(1);
    }

    80% {
        filter: brightness(4);
    }

    100% {
        filter: brightness(1);
    }
}

@keyframes lll {
    0% {
        filter: brightness(3);
    }

    100% {
        filter: brightness(1);
    }
}