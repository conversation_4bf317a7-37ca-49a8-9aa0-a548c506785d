/**
 * 角色類 - 封裝角色的所有邏輯
 */
class Character {
    constructor(playerData, cellWidth, cellHeight, mapCols = 20) {
        this.data = playerData;
        this.cellWidth = cellWidth;
        this.cellHeight = cellHeight;
        this.mapCols = mapCols;

        // 位置設置（像素為單位）
        this.x = 0;
        this.y = 0;
        this.targetX = 0;
        this.targetY = 0;

        // 移動設置
        this.speed = 250; // 像素/秒
        this.isMoving = false;

        // 狀態機
        this.state = 'idle-down';
        this.direction = 'down';

        // 動畫設置
        this.currentFrame = 0;
        this.frameTime = 0;
        this.frameInterval = 150; // 毫秒

        // 圖片資源
        this.images = {
            stand: { up: [], down: [], left: [], right: [] },
            move: { up: [], down: [], left: [], right: [] }
        };
        this.imagesLoaded = false;

        // 邊界設置
        this.width = parseInt(this.data.width) || 77;
        this.height = parseInt(this.data.height) || 122;

        console.log("Character 初始化", this.data.name);
    }

    // 預載入所有圖片
    async preloadImages() {
        console.log("開始預載入角色圖片...");

        const loadPromises = [];

        // 載入站立動畫
        for (const direction of ['up', 'down', 'left', 'right']) {
            const standFrames = this.data.Stand[direction] || [];
            for (const framePath of standFrames) {
                loadPromises.push(this.loadImage(framePath).then(img => {
                    this.images.stand[direction].push(img);
                }));
            }
        }

        // 載入移動動畫
        for (const direction of ['up', 'down', 'left', 'right']) {
            const moveFrames = this.data.MoveRes[direction] || [];
            for (const framePath of moveFrames) {
                loadPromises.push(this.loadImage(framePath).then(img => {
                    this.images.move[direction].push(img);
                }));
            }
        }

        try {
            await Promise.all(loadPromises);
            this.imagesLoaded = true;
            console.log("角色圖片預載入完成");

            // 驗證圖片載入
            console.log("載入的圖片數量:");
            for (const action of ['stand', 'move']) {
                for (const direction of ['up', 'down', 'left', 'right']) {
                    console.log(`${action}-${direction}: ${this.images[action][direction].length} 幀`);
                }
            }
        } catch (error) {
            console.error("角色圖片預載入失敗:", error);
            throw error;
        }
    }

    // 載入單張圖片
    loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
            img.src = src;
        });
    }

    // 設置位置（格子座標轉像素）
    setGridPosition(gridX, gridY) {
        this.x = gridX * this.cellWidth;
        this.y = gridY * this.cellHeight;
        this.targetX = this.x;
        this.targetY = this.y;
    }

    // 設置像素位置
    setPixelPosition(x, y) {
        this.x = x;
        this.y = y;
        this.targetX = x;
        this.targetY = y;
    }

    // 開始移動到目標位置
    startMoveTo(targetX, targetY) {
        if (this.isMoving) return false;

        this.targetX = targetX;
        this.targetY = targetY;
        this.isMoving = true;

        // 計算移動方向
        const deltaX = targetX - this.x;
        const deltaY = targetY - this.y;

        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            this.direction = deltaX > 0 ? 'right' : 'left';
        } else {
            this.direction = deltaY > 0 ? 'down' : 'up';
        }

        this.setState(`walk-${this.direction}`);
        return true;
    }

    // 設置狀態
    setState(newState) {
        if (this.state !== newState) {
            this.state = newState;
            this.currentFrame = 0;
            this.frameTime = 0;
            console.log(`角色狀態切換: ${newState}`);
        }
    }

    // 更新角色（每幀調用）
    update(deltaTime) {
        // 更新移動
        this.updateMovement(deltaTime);

        // 更新動畫
        this.updateAnimation(deltaTime);
    }

    // 更新移動
    updateMovement(deltaTime) {
        if (!this.isMoving) return;

        const deltaX = this.targetX - this.x;
        const deltaY = this.targetY - this.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        if (distance < 1) {
            // 到達目標
            this.x = this.targetX;
            this.y = this.targetY;
            this.isMoving = false;
            this.setState(`idle-${this.direction}`);
        } else {
            // 繼續移動
            const moveDistance = this.speed * deltaTime / 1000;
            const ratio = Math.min(moveDistance / distance, 1);

            this.x += deltaX * ratio;
            this.y += deltaY * ratio;
        }
    }

    // 更新動畫
    updateAnimation(deltaTime) {
        this.frameTime += deltaTime;

        if (this.frameTime >= this.frameInterval) {
            this.frameTime = 0;

            const [action, direction] = this.state.split('-');
            const actionKey = action === 'walk' ? 'move' : 'stand';
            const frames = this.images[actionKey][direction];

            if (frames && frames.length > 0) {
                this.currentFrame = (this.currentFrame + 1) % frames.length;
            }
        }
    }

    // 獲取當前幀圖片
    getCurrentFrame() {
        if (!this.imagesLoaded) return null;

        const [action, direction] = this.state.split('-');
        const actionKey = action === 'walk' ? 'move' : 'stand';
        const frames = this.images[actionKey][direction];

        if (frames && frames.length > 0) {
            return frames[this.currentFrame];
        }

        return null;
    }

    // 繪製角色
    draw(ctx) {
        const currentImg = this.getCurrentFrame();

        if (currentImg) {
            // 計算繪製位置（居中）
            const drawX = this.x + (this.cellWidth - this.width) / 2;
            const drawY = this.y + (this.cellHeight - this.height) / 2;

            ctx.drawImage(currentImg, drawX, drawY, this.width, this.height);
        } else {
            // 備用矩形
            ctx.fillStyle = 'rgba(0, 100, 255, 0.8)';
            ctx.fillRect(this.x + 10, this.y + 10, this.width - 20, this.height - 20);
        }

        // 繪製調試資訊
        ctx.fillStyle = 'white';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 2;
        ctx.strokeText(this.data.name, this.x + this.cellWidth / 2, this.y - 5);
        ctx.fillText(this.data.name, this.x + this.cellWidth / 2, this.y - 5);

        // 繪製狀態
        ctx.fillText(this.state, this.x + this.cellWidth / 2, this.y - 20);
    }

    // 檢查碰撞
    checkCollision(x, y, obstacles, mapCols, mapRows) {
        // 邊界檢查
        if (x < 0 || y < 0 ||
            x + this.cellWidth > mapCols * this.cellWidth ||
            y + this.cellHeight > mapRows * this.cellHeight) {
            return true;
        }

        // 障礙物檢查
        const gridX = Math.floor(x / this.cellWidth);
        const gridY = Math.floor(y / this.cellHeight);
        const gridPosition = gridY * mapCols + gridX;

        return obstacles.includes(gridPosition);
    }

    // 獲取格子位置
    getGridPosition() {
        if (this.cellWidth <= 0 || this.cellHeight <= 0) {
            return { x: 0, y: 0, position: 0 };
        }

        const gridX = Math.floor(this.x / this.cellWidth);
        const gridY = Math.floor(this.y / this.cellHeight);

        return {
            x: gridX,
            y: gridY,
            position: gridY * this.mapCols + gridX
        };
    }
}

/**
 * 營地場景類 - 重構後的版本
 */
class CampScene {
    constructor(campIndex = 0, playerData = {}) {
        this.campIndex = campIndex;
        this.playerData = playerData;

        // Canvas 設置
        this.canvas = null;
        this.ctx = null;
        this.cellWidth = 110;
        this.cellHeight = 88;

        // 地圖設置
        this.mapData = campdata[campIndex] || campdata[0];
        this.mapCols = this.mapData.size.cols;
        this.mapRows = this.mapData.size.rows;

        // 角色設置
        this.character = null;

        // NPC 設置
        this.npcs = [];
        this.npcElements = [];

        // 鏡頭設置
        this.cameraX = 0;
        this.cameraY = 0;

        // 觸發點設置
        this.triggerPoints = this.mapData.triggerPoints || [];

        // 鍵盤控制
        this.keyPressed = {};
        this.keydownHandler = null;
        this.keyupHandler = null;
        this.keyboardPaused = false;

        // 遊戲循環
        this.animationId = null;
        this.isRunning = false;
        this.lastTime = 0;

        // 背景圖片
        this.backgroundImg = null;

        // 載入狀態
        this.isLoaded = false;

        console.log("CampScene 初始化", { campIndex, mapData: this.mapData });
    }

    // 初始化營地場景
    async init() {
        console.log("初始化營地場景");

        try {
            // 設置Canvas
            this.setupCanvas();

            // 載入資源（包括圖片預載入）
            await this.loadResources();

            // 初始化角色
            await this.initCharacter();

            // 初始化 NPC
            await this.initNPCs();

            // 設置鍵盤控制
            this.setupKeyboardControls();

            // 設置 sidebar 按鈕支持
            this.setupSidebarSupport();

            // 設置音樂
            this.setupMusic();

            // 設置鏡頭
            this.setupCamera();

            // 標記為已載入
            this.isLoaded = true;

            // 開始遊戲循環
            this.startGameLoop();

            console.log("營地場景初始化完成");

        } catch (error) {
            console.error("營地場景初始化失敗:", error);
            throw error;
        }
    }

    // 設置Canvas
    setupCanvas() {
        // 使用現有的canvas或創建新的
        this.canvas = document.getElementById('gameCanvas') || document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');

        // 設置canvas尺寸
        this.canvas.width = this.mapCols * this.cellWidth;
        this.canvas.height = this.mapRows * this.cellHeight;
        this.canvas.id = 'gameCanvas';

        // 設置樣式
        this.canvas.style.position = 'absolute';
        this.canvas.style.left = '0';
        this.canvas.style.top = '0';
        this.canvas.style.zIndex = '10'; // 確保Canvas在上層
        this.canvas.style.pointerEvents = 'auto'; // 允許Canvas接收事件
        this.canvas.style.display = 'block';
        this.canvas.style.visibility = 'visible';

        // 確保canvas在正確的容器中
        const gameMap = document.getElementById('GameMap');
        if (gameMap && !gameMap.contains(this.canvas)) {
            gameMap.appendChild(this.canvas);
        }

        // 設置GameBoard樣式
        const gameBoard = document.getElementById('GameBoard');
        if (gameBoard) {
            gameBoard.style.width = this.mapData.width;
            gameBoard.style.height = this.mapData.height;
            gameBoard.style.display = 'grid';
            // 不設置背景圖片，讓Canvas處理背景
            gameBoard.style.backgroundImage = 'none';
            gameBoard.style.backgroundColor = 'transparent';
            gameBoard.style.gridTemplateColumns = `repeat(${this.mapCols}, 100px)`;
            gameBoard.style.gridTemplateRows = `repeat(${this.mapRows}, 80px)`;
            gameBoard.style.position = 'relative';
            gameBoard.style.zIndex = '1';
        }

        console.log(`Canvas 設置完成: ${this.canvas.width}x${this.canvas.height}`);
        console.log(`GameBoard 網格: ${this.mapCols}x${this.mapRows}`);
    }

    // 載入資源
    async loadResources() {
        console.log("載入營地場景資源");

        // 嘗試載入背景圖片，如果失敗則使用預設背景
        try {
            this.backgroundImg = await this.loadImage(this.mapData.map);
            console.log("背景圖片載入成功");
        } catch (error) {
            console.warn("背景圖片載入失敗，使用預設背景:", error.message);
            this.backgroundImg = null;
        }

        console.log("資源載入完成");
    }

    // 載入圖片的輔助函數
    loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
            img.src = src;
        });
    }

    // 初始化角色
    async initCharacter() {
        console.log("初始化營地角色");

        try {
            // 獲取 Player1 資料
            const playerData = Player1; // 固定使用 Player1

            // 創建角色實例，傳入地圖參數
            this.character = new Character(playerData, this.cellWidth, this.cellHeight, this.mapCols);

            // 預載入所有圖片
            console.log("開始預載入角色圖片...");
            await this.character.preloadImages();
            console.log("角色圖片預載入完成");

            // 設置初始位置
            const initialGridX = this.mapData.role.Position % this.mapCols;
            const initialGridY = Math.floor(this.mapData.role.Position / this.mapCols);
            this.character.setGridPosition(initialGridX, initialGridY);

            console.log("角色初始化完成", {
                name: this.character.data.name,
                position: { x: initialGridX, y: initialGridY },
                pixelPos: { x: this.character.x, y: this.character.y },
                imagesLoaded: this.character.imagesLoaded
            });

        } catch (error) {
            console.error("角色初始化失敗:", error);
            throw error;
        }
    }

    // 初始化 NPC
    async initNPCs() {
        console.log("初始化營地 NPC");

        try {
            // 獲取營地的 NPC 配置
            const npcConfigs = this.mapData.npcs || this.mapData.npc || [];

            if (npcConfigs.length === 0) {
                console.log("當前營地沒有配置 NPC");
                return;
            }

            // 清理舊的 NPC 元素
            this.cleanupNPCs();

            // 為每個 NPC 創建 DOM 元素
            for (const npcConfig of npcConfigs) {
                await this.createNPCElement(npcConfig);
            }

            console.log(`成功初始化 ${this.npcs.length} 個 NPC`);

        } catch (error) {
            console.error("NPC 初始化失敗:", error);
            throw error;
        }
    }

    // 創建 NPC DOM 元素
    async createNPCElement(npcConfig) {
        console.log("創建 NPC 元素:", npcConfig);

        // 計算 NPC 的像素位置
        const gridX = npcConfig.position % this.mapCols;
        const gridY = Math.floor(npcConfig.position / this.mapCols);
        const pixelX = gridX * this.cellWidth;
        const pixelY = gridY * this.cellHeight;

        // 創建 NPC DOM 元素
        const npcElement = document.createElement('div');
        npcElement.className = 'camp-npc';
        npcElement.id = `npc-${npcConfig.id}`;

        // 設置 NPC 樣式
        npcElement.style.cssText = `
            position: absolute;
            left: ${pixelX}px;
            top: ${pixelY}px;
            width: ${npcConfig.width || this.cellWidth}px;
            height: ${npcConfig.height || this.cellHeight}px;
            background-image: url('${npcConfig.image}');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center bottom;
            z-index: 100;
            cursor: pointer;
            transition: transform 0.2s ease;
            pointer-events: auto;
        `;

        // 創建對話圖標
        const dialogIcon = document.createElement('div');
        dialogIcon.className = 'npc-dialog-icon';
        dialogIcon.innerHTML = '💬';
        dialogIcon.style.cssText = `
            position: absolute;
            top: -15px;
            right: -10px;
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            z-index: 102;
            pointer-events: none;
            animation: bounce 1s infinite;
        `;

        // 添加彈跳動畫
        const style = document.createElement('style');
        style.textContent = `
            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-5px); }
                60% { transform: translateY(-3px); }
            }
        `;
        if (!document.head.querySelector('style[data-npc-animation]')) {
            style.setAttribute('data-npc-animation', 'true');
            document.head.appendChild(style);
        }

        npcElement.appendChild(dialogIcon);

        // 添加懸停效果 - 顯示對話圖標
        npcElement.addEventListener('mouseenter', () => {
            dialogIcon.style.display = 'flex';
        });

        npcElement.addEventListener('mouseleave', () => {
            dialogIcon.style.display = 'none';
        });

        // 添加點擊事件
        npcElement.addEventListener('click', (event) => {
            event.preventDefault();
            event.stopPropagation();
            this.handleNPCClick(npcConfig);
        });

        // 將 NPC 元素添加到遊戲地圖容器
        const gameMap = document.getElementById('GameMap');
        if (gameMap) {
            gameMap.appendChild(npcElement);

            // 保存 NPC 數據和元素引用
            const npcData = {
                config: npcConfig,
                element: npcElement,
                gridX: gridX,
                gridY: gridY,
                pixelX: pixelX,
                pixelY: pixelY
            };

            this.npcs.push(npcData);
            this.npcElements.push(npcElement);

            console.log(`NPC ${npcConfig.name || npcConfig.id} 已放置在位置 (${gridX}, ${gridY})`);
        } else {
            console.error("找不到 GameMap 容器，無法放置 NPC");
        }
    }

    // 處理 NPC 點擊事件
    handleNPCClick(npcConfig) {
        console.log("點擊 NPC:", npcConfig);

        // 根據 NPC 類型執行不同的動作
        switch (npcConfig.type) {
            case 'shop':
                this.openShop({
                    name: npcConfig.shopName || npcConfig.name,
                    position: npcConfig.position
                });
                break;

            case 'dialog':
                this.showNPCDialog(npcConfig);
                break;

            case 'quest':
                this.handleQuest(npcConfig);
                break;

            default:
                console.log(`未知的 NPC 類型: ${npcConfig.type}`);
                // 默認顯示對話
                this.showNPCDialog(npcConfig);
                break;
        }
    }

    // 顯示 NPC 對話
    showNPCDialog(npcConfig) {
        const dialog = npcConfig.dialog || `你好！我是 ${npcConfig.name || 'NPC'}。`;

        // 移除已存在的對話框
        const existingDialog = document.getElementById('npc-dialog-box');
        if (existingDialog) {
            existingDialog.remove();
        }

        // 創建底部對話框
        const dialogBox = document.createElement('div');
        dialogBox.id = 'npc-dialog-box';
        dialogBox.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(247, 231, 173, 0.95);
            color: rgb(168, 105, 38);
            border: 3px solid rgb(165, 90, 24);
            border-radius: 10px 10px 0 0;
            padding: 20px 30px;
            max-width: 600px;
            min-width: 400px;
            text-align: left;
            z-index: 1000;
            font-size: 16px;
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.3s ease-out;
        `;

        // 添加滑入動畫
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideUp {
                from {
                    transform: translateX(-50%) translateY(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(-50%) translateY(0);
                    opacity: 1;
                }
            }
        `;
        if (!document.head.querySelector('style[data-dialog-animation]')) {
            style.setAttribute('data-dialog-animation', 'true');
            document.head.appendChild(style);
        }

        dialogBox.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <div style="
                    width: 50px;
                    height: 50px;
                    background-image: url('${npcConfig.image}');
                    background-size: contain;
                    background-repeat: no-repeat;
                    background-position: center;
                    margin-right: 15px;
                    border: 2px solid rgb(165, 90, 24);
                    border-radius: 50%;
                "></div>
                <h3 style="margin: 0; color: rgb(165, 90, 24); font-size: 18px;">${npcConfig.name || 'NPC'}</h3>
                <button onclick="this.closest('#npc-dialog-box').remove()" style="
                    margin-left: auto;
                    padding: 5px 10px;
                    background: rgb(165, 90, 24);
                    color: rgb(247, 231, 173);
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: bold;
                ">✕</button>
            </div>
            <p style="margin: 0; line-height: 1.5; font-size: 14px;">${dialog}</p>
        `;

        document.body.appendChild(dialogBox);

        // 5秒後自動關閉
        setTimeout(() => {
            if (document.body.contains(dialogBox)) {
                dialogBox.style.animation = 'slideDown 0.3s ease-in forwards';
                setTimeout(() => {
                    if (document.body.contains(dialogBox)) {
                        dialogBox.remove();
                    }
                }, 300);
            }
        }, 5000);

        // 添加滑出動畫
        const slideDownStyle = document.createElement('style');
        slideDownStyle.textContent = `
            @keyframes slideDown {
                from {
                    transform: translateX(-50%) translateY(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(-50%) translateY(100%);
                    opacity: 0;
                }
            }
        `;
        if (!document.head.querySelector('style[data-dialog-slidedown]')) {
            slideDownStyle.setAttribute('data-dialog-slidedown', 'true');
            document.head.appendChild(slideDownStyle);
        }
    }

    // 處理任務
    handleQuest(npcConfig) {
        console.log("處理任務:", npcConfig.questId);
        // 這裡可以實現任務系統
        this.showNPCDialog({
            ...npcConfig,
            dialog: npcConfig.questDialog || "我有一個任務要交給你..."
        });
    }

    // 清理 NPC
    cleanupNPCs() {
        console.log("清理舊的 NPC 元素");

        // 移除所有 NPC DOM 元素
        this.npcElements.forEach(element => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });

        // 清空數組
        this.npcs = [];
        this.npcElements = [];
    }



    // 設置鍵盤控制
    setupKeyboardControls() {
        console.log("設置鍵盤控制");

        // 創建綁定的事件處理器，以便後續移除
        this.keydownHandler = (event) => {
            if (!this.isRunning || !this.isLoaded || this.keyboardPaused) return;

            this.keyPressed[event.code] = true;

            // 阻止預設行為
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'KeyW', 'KeyA', 'KeyS', 'KeyD'].includes(event.code)) {
                event.preventDefault();
            }

            // 移除特殊按鍵處理（Enter 和 Esc 已移除）
            // 如果需要其他特殊按鍵處理，可以在這裡添加
        };

        this.keyupHandler = (event) => {
            if (this.keyboardPaused) return;
            this.keyPressed[event.code] = false;

            // 鍵盤放開時，如果角色不在移動，切換為站立動畫
            if (this.character && !this.character.isMoving) {
                const isMovementKey = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'KeyW', 'KeyA', 'KeyS', 'KeyD'].includes(event.code);
                if (isMovementKey && !this.isAnyMovementKeyPressed()) {
                    this.character.setState(`idle-${this.character.direction}`);
                }
            }
        };

        // 添加事件監聽器
        document.addEventListener('keydown', this.keydownHandler);
        document.addEventListener('keyup', this.keyupHandler);

        console.log("鍵盤控制設置完成");
    }

    // 暫停鍵盤控制
    pauseKeyboardControls() {
        console.log("暫停鍵盤控制");
        this.keyboardPaused = true;

        // 清除所有當前按下的按鍵狀態，確保玩家完全停止移動
        this.keyPressed = {};

        // 如果角色正在移動，立即切換為站立狀態
        if (this.character && this.character.state.startsWith('walk-')) {
            this.character.setState(`idle-${this.character.direction}`);
        }
    }

    // 恢復鍵盤控制
    resumeKeyboardControls() {
        console.log("恢復鍵盤控制");
        this.keyboardPaused = false;

        // 確保角色處於站立狀態
        if (this.character && this.character.state.startsWith('walk-')) {
            this.character.setState(`idle-${this.character.direction}`);
        }
    }

    // 檢查是否有任何移動鍵被按下
    isAnyMovementKeyPressed() {
        return this.keyPressed['ArrowUp'] || this.keyPressed['ArrowDown'] ||
            this.keyPressed['ArrowLeft'] || this.keyPressed['ArrowRight'] ||
            this.keyPressed['KeyW'] || this.keyPressed['KeyA'] ||
            this.keyPressed['KeyS'] || this.keyPressed['KeyD'];
    }

    // 設置 sidebar 按鈕支持
    setupSidebarSupport() {
        console.log("設置營地場景 sidebar 支持");

        // 禁用存取進度和讀取進度按鈕
        this.disableSaveLoadButtons();

        // 為查看角色按鈕設置默認行為
        if (typeof Dom !== 'undefined' && Dom.LookRoleBtn) {
            Dom.LookRoleBtn.onclick = () => {
                console.log("在營地場景中查看角色");
                // 獲取第一個可用的玩家
                const availablePlayers = this.getAvailablePlayers();
                if (availablePlayers.length > 0) {
                    if (typeof lookrole === 'function') {
                        lookrole(availablePlayers[0]);
                    } else {
                        console.warn("lookrole 函數不存在");
                    }
                } else {
                    console.warn("沒有可用的玩家數據");
                }
            };
        }

        console.log("營地場景 sidebar 支持設置完成");
    }

    // 禁用營地場景中不適用的按鈕
    disableSaveLoadButtons() {
        console.log("禁用營地場景中不適用的按鈕");

        // 禁用存取進度按鈕
        const saveBtn = document.getElementById('savegamebtn');
        if (saveBtn) {
            this.disableButton(saveBtn, '營地場景中無法使用存取進度功能\n請在戰鬥場景中使用此功能');
            console.log("存取進度按鈕已禁用");
        }

        // 禁用讀取進度按鈕
        const loadBtn = document.getElementById('loadgamebtn');
        if (loadBtn) {
            this.disableButton(loadBtn, '營地場景中無法使用讀取進度功能\n請使用主選單的「前歷再續」功能');
            console.log("讀取進度按鈕已禁用");
        }

        // 禁用查看角色按鈕
        const lookRoleBtn = document.getElementById('lookrolebtn');
        if (lookRoleBtn) {
            this.disableButton(lookRoleBtn, '營地場景中無法使用查看角色功能\n請在戰鬥場景中使用此功能');
            console.log("查看角色按鈕已禁用");
        }

        // 禁用取消動作按鈕
        const cancelBtn = document.getElementById('cancelbtn');
        if (cancelBtn) {
            this.disableButton(cancelBtn, '營地場景中無法使用取消動作功能\n此功能僅在戰鬥場景中可用');
            console.log("取消動作按鈕已禁用");
        }

        // 禁用結束回合按鈕
        const endTurnBtn = document.getElementById('endturnbtn');
        if (endTurnBtn) {
            this.disableButton(endTurnBtn, '營地場景中無法使用結束回合功能\n此功能僅在戰鬥場景中可用');
            console.log("結束回合按鈕已禁用");
        }

        // 設置設定按鈕為返回主畫面
        this.setupSettingButtonForCamp();
    }

    // 通用的按鈕禁用方法
    disableButton(button, message) {
        button.style.opacity = '0.5';
        button.style.pointerEvents = 'none';
        button.style.cursor = 'not-allowed';
        button.title = message;

        // 移除原有的點擊事件，添加提示事件
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        newButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            alert(message);
        });
    }

    // 設置設定按鈕為營地專用功能
    setupSettingButtonForCamp() {
        console.log("設置營地場景專用的設定按鈕");

        const settingBtn = document.getElementById('settingbtn');
        if (settingBtn) {
            // 保存原始的點擊事件處理器
            this.originalSettingHandler = settingBtn.onclick;

            // 移除原有的點擊事件，設置新的事件
            const newSettingBtn = settingBtn.cloneNode(true);
            settingBtn.parentNode.replaceChild(newSettingBtn, settingBtn);

            newSettingBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showCampSettingMenu();
            });

            // 更新按鈕提示
            newSettingBtn.title = '營地設定選單';

            console.log("設定按鈕已設置為營地專用功能");
        }
    }

    // 顯示營地專用設定選單
    showCampSettingMenu() {
        console.log("顯示營地專用設定選單");

        // 創建設定對話框
        const settingDialog = document.createElement("dialog");
        settingDialog.id = "camp-setting-dialog";
        settingDialog.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            color: rgb(168, 105, 38);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(165, 90, 24);
            border-radius: 15px;
            padding: 30px;
            z-index: 10000;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            box-shadow: 0 0 20px rgba(165, 90, 24, 0.5);
        `;

        // 創建標題
        const title = document.createElement("h2");
        title.textContent = "營地設定";
        title.style.cssText = `
            margin: 0 0 25px 0;
            text-align: center;
            color: rgb(168, 105, 38);
            font-size: 24px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            font-weight: bold;
        `;
        settingDialog.appendChild(title);

        // 創建選項容器
        const optionsContainer = document.createElement("div");
        optionsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 15px;
        `;

        // 選項配置
        const options = [
            {
                text: "🏠 返回遊戲主畫面",
                description: "回到遊戲主選單",
                action: () => {
                    settingDialog.close();
                    document.body.removeChild(settingDialog);
                    this.returnToMainMenu();
                }
            },
            {
                text: "🔊 音效設定",
                description: "調整遊戲音效",
                action: () => {
                    settingDialog.close();
                    document.body.removeChild(settingDialog);
                    this.showAudioSettings();
                }
            },
            {
                text: "ℹ️ 遊戲說明",
                description: "查看遊戲操作說明",
                action: () => {
                    settingDialog.close();
                    document.body.removeChild(settingDialog);
                    this.showGameInstructions();
                }
            },
            {
                text: "❌ 關閉選單",
                description: "關閉此設定選單",
                action: () => {
                    settingDialog.close();
                    document.body.removeChild(settingDialog);
                }
            }
        ];

        // 創建選項按鈕
        options.forEach(option => {
            const optionButton = document.createElement("button");
            optionButton.style.cssText = `
                width: 100%;
                padding: 15px;
                color: rgb(168, 105, 38);
                background: rgb(247, 231, 173);
                border: 2px solid rgb(165, 90, 24);
                border-radius: 8px;
                cursor: pointer;
                font-size: 16px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                transition: all 0.3s ease;
                text-align: left;
            `;

            optionButton.onmouseover = () => {
                optionButton.style.background = 'rgb(255, 241, 183)';
                optionButton.style.borderColor = 'rgb(185, 110, 44)';
                optionButton.style.transform = 'translateY(-2px)';
            };

            optionButton.onmouseout = () => {
                optionButton.style.background = 'rgb(247, 231, 173)';
                optionButton.style.borderColor = 'rgb(165, 90, 24)';
                optionButton.style.transform = 'translateY(0)';
            };

            optionButton.onclick = option.action;

            // 按鈕文字容器
            const buttonContent = document.createElement("div");

            const buttonText = document.createElement("div");
            buttonText.textContent = option.text;
            buttonText.style.fontSize = "18px";
            buttonContent.appendChild(buttonText);

            const buttonDesc = document.createElement("div");
            buttonDesc.textContent = option.description;
            buttonDesc.style.cssText = `
                font-size: 14px;
                opacity: 0.8;
                margin-top: 5px;
            `;
            buttonContent.appendChild(buttonDesc);

            optionButton.appendChild(buttonContent);
            optionsContainer.appendChild(optionButton);
        });

        settingDialog.appendChild(optionsContainer);
        document.body.appendChild(settingDialog);
        settingDialog.showModal();
    }

    // 返回遊戲主畫面
    returnToMainMenu() {
        console.log("從營地返回遊戲主畫面");

        const confirmReturn = confirm("確定要返回遊戲主畫面嗎？\n未保存的進度可能會丟失。");
        if (confirmReturn) {
            // 清理當前場景
            this.cleanup();

            // 重新載入頁面回到主選單
            window.location.reload();
        }
    }

    // 顯示音效設定
    showAudioSettings() {
        console.log("顯示營地音效設定");

        // 調用全局的音效設定功能
        if (typeof window.showAudioSettings === 'function') {
            window.showAudioSettings();
        } else {
            alert("音效設定功能暫時不可用");
        }
    }

    // 顯示遊戲說明
    showGameInstructions() {
        console.log("顯示遊戲說明");

        const instructionDialog = document.createElement("dialog");
        instructionDialog.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 500px;
            max-height: 70vh;
            color: rgb(168, 105, 38);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(165, 90, 24);
            border-radius: 15px;
            padding: 30px;
            z-index: 10001;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            overflow-y: auto;
            box-shadow: 0 0 20px rgba(165, 90, 24, 0.5);
        `;

        instructionDialog.innerHTML = `
            <h2 style="color: rgb(168, 105, 38); text-align: center; margin-bottom: 20px; font-weight: bold;">營地操作說明</h2>
            <div style="line-height: 1.6; color: rgb(168, 105, 38);">
                <h3 style="color: rgb(165, 90, 24); font-weight: bold;">🎮 基本操作</h3>
                <p>• 使用方向鍵或 WASD 移動角色</p>
                <p>• 走到 NPC 附近會自動觸發對話</p>
                <p>• 走到特定位置會觸發場景切換</p>

                <h3 style="color: rgb(165, 90, 24); font-weight: bold;">🏪 營地功能</h3>
                <p>• 鐵匠鋪：購買和升級武器裝備</p>
                <p>• 雜貨店：購買消耗品和道具</p>
                <p>• 藥鋪：購買恢復道具</p>
                <p>• 客棧：休息和管理角色</p>

                <h3 style="color: rgb(165, 90, 24); font-weight: bold;">⚔️ 戰鬥準備</h3>
                <p>• 在營地中整理裝備和道具</p>
                <p>• 走到關卡入口進入戰鬥</p>
                <p>• 營地中無法使用戰鬥相關功能</p>

                <h3 style="color: rgb(165, 90, 24); font-weight: bold;">💾 存檔說明</h3>
                <p>• 營地中無法使用一般存檔功能</p>
                <p>• 請在戰鬥場景中進行存檔</p>
                <p>• 客棧中可以使用專用存檔功能</p>
            </div>
            <button onclick="closeInstructionDialog();"
                    style="width: 100%; padding: 10px; margin-top: 20px; color: rgb(168, 105, 38); background: rgb(247, 231, 173); border: 2px solid rgb(165, 90, 24); border-radius: 5px; cursor: pointer; font-size: 16px; font-weight: bold;">
                關閉說明
            </button>
        `;

        document.body.appendChild(instructionDialog);
        instructionDialog.showModal();
    }

    // 關閉遊戲說明對話框
    closeInstructionDialog() {
        const dialogs = document.querySelectorAll('dialog');
        dialogs.forEach(dialog => {
            if (dialog.innerHTML.includes('營地操作說明')) {
                dialog.close();
                document.body.removeChild(dialog);
                console.log("遊戲說明對話框已關閉");
            }
        });
    }

    // 恢復所有 sidebar 按鈕（離開營地時調用）
    enableSaveLoadButtons() {
        console.log("恢復所有 sidebar 按鈕");

        // 恢復存取進度按鈕
        const saveBtn = document.getElementById('savegamebtn');
        if (saveBtn) {
            this.enableButton(saveBtn, '存取進度');
            console.log("存取進度按鈕已恢復");
        }

        // 恢復讀取進度按鈕
        const loadBtn = document.getElementById('loadgamebtn');
        if (loadBtn) {
            this.enableButton(loadBtn, '讀取進度');
            console.log("讀取進度按鈕已恢復");
        }

        // 恢復查看角色按鈕
        const lookRoleBtn = document.getElementById('lookrolebtn');
        if (lookRoleBtn) {
            this.enableButton(lookRoleBtn, '查看角色');
            console.log("查看角色按鈕已恢復");
        }

        // 恢復取消動作按鈕
        const cancelBtn = document.getElementById('cancelbtn');
        if (cancelBtn) {
            this.enableButton(cancelBtn, '取消動作');
            console.log("取消動作按鈕已恢復");
        }

        // 恢復結束回合按鈕
        const endTurnBtn = document.getElementById('endturnbtn');
        if (endTurnBtn) {
            this.enableButton(endTurnBtn, '結束回合');
            console.log("結束回合按鈕已恢復");
        }

        // 恢復設定按鈕的原始功能
        this.restoreSettingButton();
    }

    // 通用的按鈕恢復方法
    enableButton(button, title) {
        button.style.opacity = '1';
        button.style.pointerEvents = 'auto';
        button.style.cursor = 'pointer';
        button.title = title;
    }

    // 恢復設定按鈕的原始功能
    restoreSettingButton() {
        console.log("恢復設定按鈕的原始功能");

        const settingBtn = document.getElementById('settingbtn');
        if (settingBtn) {
            // 清除營地專用的事件處理器
            const newSettingBtn = settingBtn.cloneNode(true);
            settingBtn.parentNode.replaceChild(newSettingBtn, settingBtn);

            // 恢復基本樣式
            newSettingBtn.style.opacity = '1';
            newSettingBtn.style.pointerEvents = 'auto';
            newSettingBtn.style.cursor = 'pointer';
            newSettingBtn.title = '遊戲設定';

            // 注意：不在這裡設置點擊事件，讓全局的 enableSidebarButtons 來處理
            console.log("設定按鈕已清理營地專用功能，等待全局恢復");
        }
    }

    // 獲取可用的玩家列表
    getAvailablePlayers() {
        const players = [];

        // 從場景管理器獲取玩家數據
        if (typeof sceneManager !== 'undefined' && sceneManager.gameData.playerData) {
            Object.values(sceneManager.gameData.playerData).forEach(player => {
                if (player && player.name) {
                    players.push(player);
                }
            });
        }

        // 如果沒有從場景管理器獲取到，嘗試從全局變數獲取
        if (players.length === 0) {
            if (typeof allPlayers !== 'undefined') {
                Object.values(allPlayers).forEach(player => {
                    if (player && player.name) {
                        players.push(player);
                    }
                });
            }
        }

        // 如果還是沒有，使用預設玩家數據
        if (players.length === 0) {
            if (typeof Player1 !== 'undefined') {
                players.push(Player1);
            }
            if (typeof Player2 !== 'undefined') {
                players.push(Player2);
            }
            if (typeof Player0 !== 'undefined') {
                players.push(Player0);
            }
        }

        return players;
    }

    // 檢查互動
    checkInteraction() {
        if (!this.character) return;

        const gridPos = this.character.getGridPosition();

        // 檢查是否在商店觸發點
        for (const trigger of this.triggerPoints) {
            if (trigger.type === 'shop' && trigger.position === gridPos.position) {
                console.log("在商店觸發點，開啟商店:", trigger);
                this.openShop(trigger);
                return;
            }
        }

        console.log("當前位置沒有可互動的物件");
    }

    // 顯示暫停選單
    showPauseMenu() {
        const pauseDialog = document.createElement("dialog");
        pauseDialog.id = "pauseDialog";
        pauseDialog.innerHTML = `
            <div style="padding: 20px; text-align: center; background: rgba(0,0,0,0.8); color: white; border-radius: 10px;">
                <h2>遊戲暫停</h2>
                <button id="resumeBtn" style="padding: 10px 20px; margin: 5px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    繼續遊戲
                </button>
                <button id="saveBtn" style="padding: 10px 20px; margin: 5px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    保存遊戲
                </button>
                <button id="exitBtn" style="padding: 10px 20px; margin: 5px; background: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    返回主選單
                </button>
            </div>
        `;

        document.body.appendChild(pauseDialog);
        pauseDialog.showModal();

        // 暫停遊戲
        this.isRunning = false;

        // 設置按鈕事件
        document.getElementById("resumeBtn").onclick = () => {
            pauseDialog.close();
            document.body.removeChild(pauseDialog);
            this.isRunning = true;
        };

        document.getElementById("saveBtn").onclick = () => {
            sceneManager.savePlayerData();
            sceneManager.saveGameProgress();
            alert("遊戲已保存！");
        };

        document.getElementById("exitBtn").onclick = () => {
            pauseDialog.close();
            document.body.removeChild(pauseDialog);
            window.location.reload();
        };
    }



    // 開啟商店
    openShop(trigger) {
        console.log("開啟商店:", trigger);

        // 1. 暫停鍵盤事件監聽
        this.pauseKeyboardControls();

        // 2. 找到對應的商店資料
        const shopData = this.findShopData(trigger.name);
        if (!shopData) {
            console.error("找不到商店資料:", trigger.name);
            return;
        }

        // 3. 移動玩家到商店外一格
        this.movePlayerOutsideShop(trigger.position);

        // 4. 創建商店 dialog
        this.createShopDialog(shopData);
    }

    // 找到商店資料
    findShopData(shopName) {
        // 根據商店名稱找到對應的商店資料
        const shopMapping = {
            '鐵匠鋪': 'shop1',
            '雜貨店': 'shop2',
            '藥鋪': 'shop3',
            '高級武器商店': 'shop1' // 暫時使用鐵匠鋪資料
        };

        const shopId = shopMapping[shopName];
        if (!shopId) return null;

        return shopdata.find(shop => shop.id === shopId);
    }

    // 移動玩家到商店外一格
    movePlayerOutsideShop(shopPosition) {
        if (!this.character) return;

        // 計算商店外一格的位置（向下一格）
        const outsidePosition = shopPosition + this.mapCols;
        const outsideGridX = outsidePosition % this.mapCols;
        const outsideGridY = Math.floor(outsidePosition / this.mapCols);

        // 檢查目標位置是否有效且無障礙物
        if (outsideGridY < this.mapRows &&
            !(this.mapData.obstacles || []).includes(outsidePosition)) {

            this.character.setGridPosition(outsideGridX, outsideGridY);
            this.updateCamera();
            console.log(`玩家移動到商店外: 格子(${outsideGridX}, ${outsideGridY}), 位置${outsidePosition}`);
        } else {
            console.warn("商店外位置無效或有障礙物，玩家位置不變");
        }
    }

    // 創建商店對話框
    createShopDialog(shopData) {
        console.log("創建商店對話框:", shopData);

        // 創建 dialog 元素
        const shopDialog = document.createElement("dialog");
        shopDialog.id = "shopDialog";
        shopDialog.style.cssText = `
            border: none;
            width: 100vw;
            height: 100vh;
            max-width: 100vw;
            max-height: 100vh;
            padding: 0;
            margin: 0;
            background: rgba(0, 0, 0, 1);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
        `;

        // 創建商店內容
        const shopContent = document.createElement("div");
        shopContent.style.cssText = `
            width: 90%;
            height: 95%;
            background-image: url('${shopData.bg}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
            display: flex;
            flex-direction: column;
            padding: 40px;
            box-sizing: border-box;
        `;

        const shopmidarea = document.createElement("div");
        shopmidarea.style.cssText = `
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100%;
            opacity: 0;
            animation: shopappear 1.5s forwards ease-in-out;
            animation-delay: 7s;
        `;

        //玩家背包容器
        const playerBagContainer = document.createElement("div");
        playerBagContainer.id = "playerBagContainer"; // 設置 ID 以便後續更新
        playerBagContainer.style.cssText = `
            flex: 1;
            position: relative;
            float:left;
            overflow-y: auto;
            background:url(./Public/mybagwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 40%;
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;

        // 背包標題容器
        const bagTitleContainer = document.createElement("div");
        bagTitleContainer.style.cssText = `
            position: absolute;
            width: 30%;
            height: 10%;
            margin-left: 50px;
            margin-top: -12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        `;

        // 上一位按鈕
        const prevPlayerBtn = document.createElement("button");
        prevPlayerBtn.textContent = "◀";
        prevPlayerBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 背包標題
        const playerBagTitle = document.createElement("div");
        playerBagTitle.id = "bagTitle";
        playerBagTitle.textContent = "行囊";
        playerBagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            font-weight: bold;
            letter-spacing: 5px;
            flex: 1;
        `;

        // 下一位按鈕
        const nextPlayerBtn = document.createElement("button");
        nextPlayerBtn.textContent = "▶";
        nextPlayerBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 組裝標題容器
        bagTitleContainer.appendChild(prevPlayerBtn);
        bagTitleContainer.appendChild(playerBagTitle);
        bagTitleContainer.appendChild(nextPlayerBtn);
        playerBagContainer.appendChild(bagTitleContainer);

        // 初始化背包切換系統
        this.initBagSwitchSystem(prevPlayerBtn, nextPlayerBtn, playerBagTitle);

        // 確保初始渲染也有賣出功能
        this.renderCurrentBag();

        const playerBagItemsContainer = document.createElement("div");
        playerBagItemsContainer.style.cssText = `
            margin-left: 6%;
            margin-top: 10%;
            width: 90%;
            height: 72%;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        playerBagContainer.appendChild(playerBagItemsContainer);

        const itemMap = new Map();

        mybag.forEach(item => {
            if (itemMap.has(item.name)) {
                const existing = itemMap.get(item.name);
                existing.count += 1;
            } else {
                itemMap.set(item.name, {
                    price: item.price,
                    count: 1
                });
            }
        });


        itemMap.forEach((data, name) => {
            const itemDiv = document.createElement("div");
            itemDiv.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            margin-bottom: 10px;
            cursor: pointer;
            font-size: 24px;
            color: rgb(168, 105, 38);
            box-sizing: border-box;
            border: 3px solid transparent;
        `;

            itemDiv.innerHTML = `
            <span style="font-weight: bold; color: rgb(168, 105, 38);">${name} 【${data.count}件】</span>
            <span style="color: #B8860B; font-weight: bold;">🪙 ${data.price / 2}</span>
            `;

            itemDiv.addEventListener('mouseenter', () => {
                itemDiv.style.borderColor = 'brown';
            });

            itemDiv.addEventListener('mouseleave', () => {
                itemDiv.style.borderColor = 'transparent';
            });

            // 添加點擊事件來賣出物品（初始渲染也要有賣出功能）
            itemDiv.addEventListener('click', () => {
                // 創建一個臨時的物品對象，包含必要的信息
                const tempItem = {
                    name: name,
                    price: data.price
                };
                this.showSellConfirmDialog(tempItem, 1, name);
            });

            playerBagItemsContainer.appendChild(itemDiv);
        });


        // 商品列表容器
        const itemsContainer = document.createElement("div");
        itemsContainer.style.cssText = `
            flex: 1;
            position: relative;
            float:right;
            overflow-y: auto;
            background:url(./Public/shopwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 40%;
            height: 100%;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        const shopTitle = document.createElement("div");
        shopTitle.textContent = shopData.name;
        shopTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            margin-top: 5px;
            width:30%;
            height: 10%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            letter-spacing: 5px;
            position: relative;
            float: right;
            right: 70px;
        `;
        itemsContainer.appendChild(shopTitle);


        const itemsarea = document.createElement("div");
        itemsarea.style.cssText = `
            margin-left: 2%;
            margin-top: 12%;
            width: 88%;
            height: 72%;
            outline:none;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        itemsContainer.appendChild(itemsarea);

        // 添加商品
        shopData.items.forEach(item => {
            const itemDiv = document.createElement("div");
            itemDiv.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            margin-bottom: 10px;
            cursor: pointer;
            font-size: 24px;
            color: rgb(168, 105, 38);
            box-sizing: border-box;
            border: 3px solid transparent;  // 預設透明
            `;


            itemDiv.innerHTML = `
                <span style="font-weight: bold; color: rgb(168, 105, 38);;">${item.name}</span>
                <span style="color: #B8860B; font-weight: bold;">🪙 ${item.price}</span>
                
            `;

            // 商品點擊事件
            itemDiv.addEventListener('click', () => {
                this.buyItem(item);
            });

            itemDiv.addEventListener('mouseenter', () => {
                itemDiv.style.borderColor = 'brown';  // 只改顏色
            });

            itemDiv.addEventListener('mouseleave', () => {
                itemDiv.style.borderColor = 'transparent';
            });
            itemsarea.appendChild(itemDiv);
        });

        // 關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.textContent = "離開商店";
        closeButton.style.cssText = `
            padding: 10px 20px;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            font-size: 25px;
            font-weight: bold;
            cursor: pointer;
            align-self: center;
            transition: background-color 0.3s ease;
            opacity: 0;
            animation: shopappear 1.5s forwards ease-in-out;
            animation-delay: 7s;
        `;

        closeButton.addEventListener('click', () => {
            this.closeShop(shopDialog);
        });

        closeButton.addEventListener('mouseenter', () => {
            closeButton.style.backgroundColor = 'rgb(165, 90, 24)';
            closeButton.style.color = 'white';
            closeButton.style.transform = 'scale(1.05)';
        });

        closeButton.addEventListener('mouseleave', () => {
            closeButton.style.backgroundColor = 'rgb(247, 231, 173)';
            closeButton.style.color = 'rgb(168, 105, 38)';
            closeButton.style.transform = 'scale(1)';
        });

        const mymoneyshow = document.createElement("div");
        mymoneyshow.id = "mymoneyshow"; // 設置 ID 以便後續更新
        mymoneyshow.textContent = `🪙 ${mymoney}`;
        mymoneyshow.style.cssText = `
            position: absolute;
            bottom: 10px;
            right: 10px;
            color: white;
            font-size: 30px;
            font-weight: bold;
            backdrop-filter: blur(10px);
            border-radius: 20px
        `;

        shopContent.appendChild(mymoneyshow);

        // 組裝對話框
        shopmidarea.appendChild(itemsContainer);

        shopmidarea.appendChild(playerBagContainer);
        shopContent.appendChild(shopmidarea);
        shopDialog.appendChild(shopContent);
        shopContent.appendChild(closeButton);
        // 添加到頁面並顯示
        document.body.appendChild(shopDialog);

        shopDialog.showModal();

        // 添加 Esc 鍵退出商店功能
        this.setupShopEscapeHandler(shopDialog);

        console.log("商店對話框已創建並顯示");
    }

    // 設置商店 Esc 鍵處理
    setupShopEscapeHandler(shopDialog) {
        const escapeHandler = (event) => {
            if (event.code === 'Escape') {
                event.preventDefault();
                event.stopPropagation();
                this.closeShop(shopDialog);
                // 移除事件監聽器
                document.removeEventListener('keydown', escapeHandler);
                console.log("按下 Esc 鍵，退出商店");
            }
        };

        // 添加事件監聽器
        document.addEventListener('keydown', escapeHandler);

        // 保存事件處理器引用，以便在商店關閉時清理
        shopDialog.escapeHandler = escapeHandler;
    }

    // 初始化背包切換系統
    initBagSwitchSystem(prevBtn, nextBtn, titleElement) {
        console.log("初始化背包切換系統");

        // 獲取當前關卡的非 AI 玩家
        this.availablePlayers = this.getNonAIPlayers();

        // 添加公共背包到列表開頭
        this.bagOptions = [
            { type: 'public', name: '行囊', data: mybag },
            ...this.availablePlayers.map(player => ({
                type: 'player',
                name: player.name,
                data: player.Inventory,
                player: player
            }))
        ];

        // 當前選中的背包索引
        this.currentBagIndex = 0;

        console.log("可用背包選項:", this.bagOptions.map(bag => bag.name));

        // 設置按鈕事件
        prevBtn.addEventListener('click', () => {
            this.switchToPreviousBag(titleElement);
        });

        nextBtn.addEventListener('click', () => {
            this.switchToNextBag(titleElement);
        });

        // 添加按鈕懸停效果
        this.addButtonHoverEffects(prevBtn, nextBtn);

        // 初始化顯示
        this.updateBagDisplay(titleElement);
    }

    // 獲取當前關卡的非 AI 玩家
    getNonAIPlayers() {
        console.log("獲取當前關卡的非 AI 玩家");

        // 從當前關卡數據獲取玩家
        if (typeof controlLayer !== 'undefined' && typeof currentLevel !== 'undefined' &&
            controlLayer[currentLevel] && controlLayer[currentLevel].Players) {

            const levelPlayers = controlLayer[currentLevel].Players;
            const nonAIPlayers = [];

            levelPlayers.forEach(playerTemplate => {
                // 根據 ID 找到對應的玩家數據
                let playerData = null;

                // 先從全局玩家變數中查找
                if (typeof Player0 !== 'undefined' && Player0.id === playerTemplate.id && !Player0["是否電腦操作"]) {
                    playerData = Player0;
                } else if (typeof Player1 !== 'undefined' && Player1.id === playerTemplate.id && !Player1["是否電腦操作"]) {
                    playerData = Player1;
                } else if (typeof Player2 !== 'undefined' && Player2.id === playerTemplate.id && !Player2["是否電腦操作"]) {
                    playerData = Player2;
                } else if (typeof Player3 !== 'undefined' && Player3.id === playerTemplate.id && !Player3["是否電腦操作"]) {
                    playerData = Player3;
                }

                if (playerData) {
                    nonAIPlayers.push(playerData);
                    console.log(`找到非 AI 玩家: ${playerData.name} (${playerData.id})`);
                }
            });

            return nonAIPlayers;
        }

        console.warn("無法獲取當前關卡的玩家資料");
        return [];
    }

    // 切換到上一個背包
    switchToPreviousBag(titleElement) {
        if (this.bagOptions.length <= 1) return;

        this.currentBagIndex = (this.currentBagIndex - 1 + this.bagOptions.length) % this.bagOptions.length;
        console.log(`切換到上一個背包: ${this.bagOptions[this.currentBagIndex].name}`);

        this.updateBagDisplay(titleElement);
        this.renderCurrentBag();
    }

    // 切換到下一個背包
    switchToNextBag(titleElement) {
        if (this.bagOptions.length <= 1) return;

        this.currentBagIndex = (this.currentBagIndex + 1) % this.bagOptions.length;
        console.log(`切換到下一個背包: ${this.bagOptions[this.currentBagIndex].name}`);

        this.updateBagDisplay(titleElement);
        this.renderCurrentBag();
    }

    // 更新背包標題顯示
    updateBagDisplay(titleElement) {
        const currentBag = this.bagOptions[this.currentBagIndex];

        if (currentBag.type === 'player') {
            // 顯示個人背包容量信息
            const currentCount = currentBag.data.length;
            const maxCount = 6;
            titleElement.textContent = `${currentBag.name}`;
        } else {
            // 公共背包不顯示容量限制
            titleElement.textContent = currentBag.name;
        }
    }

    // 添加按鈕懸停效果
    addButtonHoverEffects(prevBtn, nextBtn) {
        // 上一位按鈕懸停效果
        prevBtn.addEventListener('mouseenter', () => {
            prevBtn.style.backgroundColor = 'rgba(165, 90, 24, 1)';
            prevBtn.style.transform = 'scale(1.1)';
        });

        prevBtn.addEventListener('mouseleave', () => {
            prevBtn.style.backgroundColor = 'rgba(168, 105, 38, 0.8)';
            prevBtn.style.transform = 'scale(1)';
        });

        // 下一位按鈕懸停效果
        nextBtn.addEventListener('mouseenter', () => {
            nextBtn.style.backgroundColor = 'rgba(165, 90, 24, 1)';
            nextBtn.style.transform = 'scale(1.1)';
        });

        nextBtn.addEventListener('mouseleave', () => {
            nextBtn.style.backgroundColor = 'rgba(168, 105, 38, 0.8)';
            nextBtn.style.transform = 'scale(1)';
        });
    }

    // 渲染當前選中的背包
    renderCurrentBag() {
        console.log("渲染當前選中的背包");

        const currentBag = this.bagOptions[this.currentBagIndex];

        // 找到背包項目容器
        const bagContainer = document.getElementById("playerBagContainer");
        if (!bagContainer) {
            console.warn("找不到背包容器");
            return;
        }

        const bagItemsContainer = bagContainer.querySelector('div[style*="margin-left: 6%"]');
        if (!bagItemsContainer) {
            console.warn("找不到背包項目容器");
            return;
        }

        // 清空背包項目容器
        bagItemsContainer.innerHTML = '';

        // 重新構建背包內容（使用原有樣式）
        const itemMap = new Map();

        // 統計當前背包中的物品數量和價格
        if (currentBag.data && currentBag.data.length > 0) {
            currentBag.data.forEach(item => {
                if (itemMap.has(item.name)) {
                    const existingData = itemMap.get(item.name);
                    existingData.count += 1;
                    itemMap.set(item.name, existingData);
                } else {
                    itemMap.set(item.name, {
                        count: 1,
                        price: item.price || 0,
                        item: item
                    });
                }
            });
        }

        // 顯示背包物品（使用原有樣式）
        if (itemMap.size > 0) {
            itemMap.forEach((data, name) => {
                const itemDiv = document.createElement("div");
                itemDiv.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 20px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    font-size: 24px;
                    color: rgb(168, 105, 38);
                    box-sizing: border-box;
                    border: 3px solid transparent;
                `;

                itemDiv.innerHTML = `
                    <span style="font-weight: bold; color: rgb(168, 105, 38);">${name} 【${data.count}件】</span>
                    <span style="color: #B8860B; font-weight: bold;">🪙 ${data.price / 2}</span>
                `;

                itemDiv.addEventListener('mouseenter', () => {
                    itemDiv.style.borderColor = 'brown';
                });

                itemDiv.addEventListener('mouseleave', () => {
                    itemDiv.style.borderColor = 'transparent';
                });

                // 添加點擊事件來賣出物品（一次只賣一件）
                itemDiv.addEventListener('click', () => {
                    this.showSellConfirmDialog(data.item, 1, name);
                });

                bagItemsContainer.appendChild(itemDiv);
            });
        } else {
            const emptyMsg = document.createElement("div");
            emptyMsg.textContent = ``;
            emptyMsg.style.cssText = `
                color: #999;
                text-align: center;
                font-style: italic;
                padding: 20px;
                font-size: 14px;
            `;
            bagItemsContainer.appendChild(emptyMsg);
        }

        console.log(`${currentBag.name}顯示已更新，物品數量:`, itemMap.size);
    }

    // 添加物品到當前選中的背包
    addItemToCurrentBag(item) {
        console.log("添加物品到當前背包:", item);

        // 檢查背包切換系統是否已初始化
        if (!this.bagOptions || this.bagOptions.length === 0) {
            // 如果沒有初始化，默認添加到公共背包
            mybag.push(item);
            return { success: true, message: "物品已添加到行囊" };
        }

        const currentBag = this.bagOptions[this.currentBagIndex];
        console.log(`當前選中的背包: ${currentBag.name} (${currentBag.type})`);

        if (currentBag.type === 'public') {
            // 添加到公共背包（行囊）
            mybag.push(item);
            console.log("物品已添加到公共背包");
            return { success: true, message: "物品已添加到行囊" };

        } else if (currentBag.type === 'player') {
            // 添加到個人背包
            const playerInventory = currentBag.data;

            // 檢查個人背包是否已滿（最多6個）
            if (playerInventory.length >= 6) {
                console.warn(`${currentBag.name}的背包已滿`);
                return {
                    success: false,
                    message: `${currentBag.name}的背包已滿！\n個人背包最多只能放6個物品。`
                };
            }

            // 添加到個人背包
            playerInventory.push(item);
            console.log(`物品已添加到${currentBag.name}的背包`);
            return {
                success: true,
                message: `物品已添加到${currentBag.name}的背包`
            };
        }

        // 未知背包類型，默認添加到公共背包
        console.warn("未知的背包類型，添加到公共背包");
        mybag.push(item);
        return { success: true, message: "物品已添加到行囊" };
    }

    // 顯示賣出確認對話框
    showSellConfirmDialog(item, count, itemName) {
        console.log("顯示賣出確認對話框:", item, count, itemName);

        // 計算賣出價格（原價的一半，固定賣出1件）
        const sellPrice = Math.floor(item.price / 2);
        const sellCount = 1; // 固定賣出1件
        const totalPrice = sellPrice;

        // 創建確認對話框
        const confirmDialog = document.createElement("dialog");
        confirmDialog.id = "sellConfirmDialog";
        confirmDialog.style.cssText = `
            border: none;
            border-radius: 10px;
            padding: 0;
            background: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            max-width: none;
            max-height: none;
            width: auto;
            height: auto;
        `;

        // 對話框內容
        confirmDialog.innerHTML = `
            <div style="
                padding: 30px;
                text-align: center;
                background-color: rgb(247, 231, 173);
                color: rgb(168, 105, 38);
                border: 5px solid rgb(165, 90, 24);
                border-radius: 10px;
                min-width: 350px;
                font-family: Arial, sans-serif;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                position: relative;
                z-index: 1000;
            ">
                <h3 style="color: rgb(165, 90, 24); margin: 0 0 20px 0; font-size: 24px; font-weight: bold;">賣出確認</h3>
                <p style="margin: 15px 0; font-size: 18px; font-weight: bold;">
                    確定要賣出 <span style="color: rgb(165, 90, 24); text-decoration: underline;">${itemName}</span> 嗎？
                </p>
                <p style="margin: 15px 0; font-size: 16px; color: rgb(165, 90, 24); font-weight: bold;">
                    數量：${sellCount} 件
                </p>
                <p style="margin: 15px 0; font-size: 16px; color: rgb(165, 90, 24); font-weight: bold;">
                    價格：🪙 ${sellPrice} 金幣 (原價的一半)
                </p>
                <p style="margin: 15px 0; font-size: 14px; color: rgb(168, 105, 38);">
                    目前金幣：🪙 ${mymoney}
                </p>
                <div style="margin-top: 25px; display: flex; gap: 20px; justify-content: center;">
                    <button id="confirmSellBtn" style="
                        padding: 12px 25px;
                        background-color: rgb(165, 90, 24);
                        color: rgb(247, 231, 173);
                        border: 3px solid rgb(168, 105, 38);
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">確定賣出</button>
                    <button id="cancelSellBtn" style="
                        padding: 12px 25px;
                        background-color: rgb(247, 231, 173);
                        color: rgb(165, 90, 24);
                        border: 3px solid rgb(165, 90, 24);
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">取消</button>
                </div>
            </div>
        `;

        // 添加到頁面並顯示
        document.body.appendChild(confirmDialog);
        confirmDialog.showModal();

        // 設置按鈕事件和懸停效果
        const confirmBtn = document.getElementById("confirmSellBtn");
        const cancelBtn = document.getElementById("cancelSellBtn");

        confirmBtn.onclick = () => {
            this.confirmSell(item, sellCount, itemName, totalPrice, confirmDialog);
        };

        // 確定按鈕懸停效果
        confirmBtn.addEventListener('mouseenter', () => {
            confirmBtn.style.backgroundColor = 'rgb(140, 70, 15)';
            confirmBtn.style.transform = 'scale(1.05)';
        });

        confirmBtn.addEventListener('mouseleave', () => {
            confirmBtn.style.backgroundColor = 'rgb(165, 90, 24)';
            confirmBtn.style.transform = 'scale(1)';
        });

        cancelBtn.onclick = () => {
            confirmDialog.close();
            document.body.removeChild(confirmDialog);
        };

        // 取消按鈕懸停效果
        cancelBtn.addEventListener('mouseenter', () => {
            cancelBtn.style.backgroundColor = 'rgb(220, 200, 150)';
            cancelBtn.style.transform = 'scale(1.05)';
        });

        cancelBtn.addEventListener('mouseleave', () => {
            cancelBtn.style.backgroundColor = 'rgb(247, 231, 173)';
            cancelBtn.style.transform = 'scale(1)';
        });
    }

    // 確認賣出物品
    confirmSell(item, count, itemName, totalPrice, confirmDialog) {
        console.log("確認賣出物品:", item, count, itemName, totalPrice);

        // 從當前背包中移除物品
        const removeResult = this.removeItemFromCurrentBag(item, count);
        if (!removeResult.success) {
            alert(removeResult.message);
            confirmDialog.close();
            document.body.removeChild(confirmDialog);
            return;
        }

        // 增加金錢
        mymoney += totalPrice;

        console.log(`賣出成功！${itemName} x${count}，獲得金幣：${totalPrice}，目前金幣：${mymoney}`);

        // 關閉確認對話框
        confirmDialog.close();
        document.body.removeChild(confirmDialog);

        // 顯示賣出成功訊息
        this.showSellSuccessMessage(itemName, 1, totalPrice);

        // 重新渲染商店界面（更新金幣和背包顯示）
        this.refreshShopDisplay();
    }

    // 從當前背包中移除物品
    removeItemFromCurrentBag(item, count) {
        console.log("從當前背包移除物品:", item, count);

        // 檢查背包切換系統是否已初始化
        if (!this.bagOptions || this.bagOptions.length === 0 || this.currentBagIndex === undefined) {
            // 如果沒有初始化，默認從公共背包移除
            console.log("背包切換系統未初始化，從公共背包移除");
            return this.removeItemsFromBag(mybag, item, "行囊");
        }

        const currentBag = this.bagOptions[this.currentBagIndex];
        console.log(`從 ${currentBag.name} 移除物品`);

        if (currentBag.type === 'public') {
            // 從公共背包移除
            return this.removeItemsFromBag(mybag, item, "行囊");

        } else if (currentBag.type === 'player') {
            // 從個人背包移除
            return this.removeItemsFromBag(currentBag.data, item, currentBag.name);
        }

        // 未知背包類型，默認從公共背包移除
        console.warn("未知的背包類型，從公共背包移除");
        return this.removeItemsFromBag(mybag, item, "行囊");
    }

    // 從指定背包中移除物品（固定移除1件）
    removeItemsFromBag(bagData, item, bagName) {
        console.log(`從 ${bagName} 移除 ${item.name} x1`);

        // 從後往前遍歷，找到第一個匹配的物品並移除
        for (let i = bagData.length - 1; i >= 0; i--) {
            if (bagData[i].name === item.name) {
                bagData.splice(i, 1);
                console.log(`成功從 ${bagName} 移除 ${item.name} x1`);
                return { success: true, message: `已從 ${bagName} 移除物品` };
            }
        }

        console.warn(`移除失敗：在 ${bagName} 中找不到 ${item.name}`);
        return { success: false, message: `背包中沒有 ${item.name}` };
    }

    // 顯示賣出成功訊息
    showSellSuccessMessage(itemName, count, totalPrice) {
        // 創建成功訊息對話框
        const successDialog = document.createElement("dialog");
        successDialog.id = "sellSuccessDialog";
        successDialog.style.cssText = `
            border: none;
            border-radius: 10px;
            padding: 0;
            background: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            max-width: none;
            max-height: none;
            width: auto;
            height: auto;
        `;

        // 對話框內容
        successDialog.innerHTML = `
            <div style="
                padding: 30px;
                text-align: center;
                background-color: rgb(247, 231, 173);
                color: rgb(168, 105, 38);
                border: 5px solid rgb(165, 90, 24);
                border-radius: 10px;
                min-width: 300px;
                font-family: Arial, sans-serif;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                position: relative;
                z-index: 1000;
            ">
                <h3 style="color: rgb(165, 90, 24); margin: 0 0 15px 0; font-size: 22px; font-weight: bold;">💰 賣出成功！</h3>
                <p style="margin: 12px 0; font-size: 16px; font-weight: bold;">
                    已賣出 <span style="color: rgb(165, 90, 24); text-decoration: underline;">${itemName}</span> x${count}
                </p>
                <p style="margin: 12px 0; font-size: 14px; color: rgb(168, 105, 38);">
                    獲得金幣：🪙 ${totalPrice}
                </p>
                <p style="margin: 12px 0; font-size: 14px; color: rgb(168, 105, 38);">
                    目前金幣：🪙 ${mymoney}
                </p>
                <button id="closeSellSuccessBtn" style="
                    margin-top: 20px;
                    padding: 10px 20px;
                    background-color: rgb(165, 90, 24);
                    color: rgb(247, 231, 173);
                    border: 3px solid rgb(168, 105, 38);
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: bold;
                    transition: all 0.3s ease;
                ">確定</button>
            </div>
        `;

        // 添加到頁面並顯示
        document.body.appendChild(successDialog);
        successDialog.showModal();

        // 設置關閉按鈕事件
        const closeBtn = document.getElementById("closeSellSuccessBtn");
        closeBtn.onclick = () => {
            successDialog.close();
            document.body.removeChild(successDialog);
        };

        // 按鈕懸停效果
        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.backgroundColor = 'rgb(140, 70, 15)';
            closeBtn.style.transform = 'scale(1.05)';
        });

        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.backgroundColor = 'rgb(165, 90, 24)';
            closeBtn.style.transform = 'scale(1)';
        });

        // 3秒後自動關閉
        setTimeout(() => {
            if (document.body.contains(successDialog)) {
                successDialog.close();
                document.body.removeChild(successDialog);
            }
        }, 3000);
    }

    // 購買物品
    buyItem(item) {
        console.log("嘗試購買物品:", item);

        // 顯示購買確認對話框
        this.showBuyConfirmDialog(item);
    }

    // 顯示購買確認對話框
    showBuyConfirmDialog(item) {
        console.log("顯示購買確認對話框:", item);

        // 創建確認對話框
        const confirmDialog = document.createElement("dialog");
        confirmDialog.id = "buyConfirmDialog";
        confirmDialog.style.cssText = `
            border: none;
            border-radius: 10px;
            padding: 0;
            background: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            max-width: none;
            max-height: none;
            width: auto;
            height: auto;
        `;

        // 對話框內容
        confirmDialog.innerHTML = `
            <div style="
                padding: 30px;
                text-align: center;
                background-color: rgb(247, 231, 173);
                color: rgb(168, 105, 38);
                border: 5px solid rgb(165, 90, 24);
                border-radius: 10px;
                min-width: 350px;
                font-family: Arial, sans-serif;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                position: relative;
                z-index: 1000;
            ">
                <h3 style="color: rgb(165, 90, 24); margin: 0 0 20px 0; font-size: 24px; font-weight: bold;">購買確認</h3>
                <p style="margin: 15px 0; font-size: 18px; font-weight: bold;">
                    確定要購買 <span style="color: rgb(165, 90, 24); text-decoration: underline;">${item.name}</span> 嗎？
                </p>
                <p style="margin: 15px 0; font-size: 16px; color: rgb(165, 90, 24); font-weight: bold;">
                    價格：🪙 ${item.price} 金幣
                </p>
                <p style="margin: 15px 0; font-size: 14px; color: rgb(168, 105, 38);">
                    目前金幣：🪙 ${mymoney}
                </p>
                <div style="margin-top: 25px; display: flex; gap: 20px; justify-content: center;">
                    <button id="confirmBuyBtn" style="
                        padding: 12px 25px;
                        background-color: rgb(165, 90, 24);
                        color: rgb(247, 231, 173);
                        border: 3px solid rgb(168, 105, 38);
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">確定購買</button>
                    <button id="cancelBuyBtn" style="
                        padding: 12px 25px;
                        background-color: rgb(247, 231, 173);
                        color: rgb(165, 90, 24);
                        border: 3px solid rgb(165, 90, 24);
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">取消</button>
                </div>
            </div>
        `;

        // 添加到頁面並顯示
        document.body.appendChild(confirmDialog);
        confirmDialog.showModal();

        // 設置按鈕事件和懸停效果
        const confirmBtn = document.getElementById("confirmBuyBtn");
        const cancelBtn = document.getElementById("cancelBuyBtn");

        confirmBtn.onclick = () => {
            this.confirmPurchase(item, confirmDialog);
        };

        // 確定按鈕懸停效果
        confirmBtn.addEventListener('mouseenter', () => {
            confirmBtn.style.backgroundColor = 'rgb(140, 70, 15)';
            confirmBtn.style.transform = 'scale(1.05)';
        });

        confirmBtn.addEventListener('mouseleave', () => {
            confirmBtn.style.backgroundColor = 'rgb(165, 90, 24)';
            confirmBtn.style.transform = 'scale(1)';
        });

        cancelBtn.onclick = () => {
            confirmDialog.close();
            document.body.removeChild(confirmDialog);
        };

        // 取消按鈕懸停效果
        cancelBtn.addEventListener('mouseenter', () => {
            cancelBtn.style.backgroundColor = 'rgb(220, 200, 150)';
            cancelBtn.style.transform = 'scale(1.05)';
        });

        cancelBtn.addEventListener('mouseleave', () => {
            cancelBtn.style.backgroundColor = 'rgb(247, 231, 173)';
            cancelBtn.style.transform = 'scale(1)';
        });
    }

    // 確認購買
    confirmPurchase(item, confirmDialog) {
        console.log("確認購買物品:", item);

        // 檢查金錢是否足夠
        if (mymoney < item.price) {
            alert("金幣不足！");
            confirmDialog.close();
            document.body.removeChild(confirmDialog);
            return;
        }

        // 扣除金錢
        mymoney -= item.price;

        // 根據當前選中的背包添加物品
        const addResult = this.addItemToCurrentBag(item);
        if (!addResult.success) {
            // 如果添加失敗，退還金錢
            mymoney += item.price;
            alert(addResult.message);
            confirmDialog.close();
            document.body.removeChild(confirmDialog);
            return;
        }

        console.log(`購買成功！${item.name} 已添加到背包，剩餘金幣：${mymoney}`);

        // 關閉確認對話框
        confirmDialog.close();
        document.body.removeChild(confirmDialog);

        // 顯示購買成功訊息
        this.showPurchaseSuccessMessage(item, addResult.message);

        // 重新渲染商店界面（更新金幣和背包顯示）
        this.refreshShopDisplay();
    }



    // 顯示購買成功訊息
    showPurchaseSuccessMessage(item, bagMessage = "物品已添加到背包") {
        // 創建成功訊息對話框
        const successDialog = document.createElement("dialog");
        successDialog.id = "purchaseSuccessDialog";
        successDialog.style.cssText = `
            border: none;
            border-radius: 10px;
            padding: 0;
            background: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            max-width: none;
            max-height: none;
            width: auto;
            height: auto;
        `;

        successDialog.innerHTML = `
            <div style="
                padding: 25px;
                text-align: center;
                background-color: rgb(247, 231, 173);
                color: rgb(168, 105, 38);
                border: 5px solid rgb(165, 90, 24);
                border-radius: 10px;
                min-width: 300px;
                font-family: Arial, sans-serif;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
                position: relative;
                z-index: 1000;
            ">
                <h3 style="color: rgb(165, 90, 24); margin: 0 0 15px 0; font-size: 22px; font-weight: bold;">✅ 購買成功！</h3>
                <p style="margin: 12px 0; font-size: 16px; font-weight: bold;">
                    已獲得 <span style="color: rgb(165, 90, 24); text-decoration: underline;">${item.name}</span>
                </p>
                <p style="margin: 12px 0; font-size: 14px; color: rgb(168, 105, 38);">
                    ${bagMessage}
                </p>
                <p style="margin: 12px 0; font-size: 14px; color: rgb(168, 105, 38);">
                    剩餘金幣：🪙 ${mymoney}
                </p>
            </div>
        `;

        document.body.appendChild(successDialog);
        successDialog.showModal();

        // 2秒後自動關閉
        setTimeout(() => {
            successDialog.close();
            document.body.removeChild(successDialog);
        }, 2000);
    }

    // 重新渲染商店顯示
    refreshShopDisplay() {
        console.log("重新渲染商店顯示");

        // 更新金幣顯示
        const moneyDisplay = document.getElementById("mymoneyshow");
        if (moneyDisplay) {
            moneyDisplay.textContent = `🪙 ${mymoney}`;
            console.log("金幣顯示已更新:", mymoney);
        } else {
            console.warn("找不到金幣顯示元素");
        }

        // 重新生成背包內容
        this.updatePlayerBagDisplay();

        console.log("商店顯示已更新");
    }

    // 更新玩家背包顯示
    updatePlayerBagDisplay() {
        console.log("更新玩家背包顯示");

        // 如果背包切換系統已初始化，使用新的渲染方法
        if (this.bagOptions && this.bagOptions.length > 0 && this.currentBagIndex !== undefined) {
            this.renderCurrentBag();
            return;
        }

        // 否則使用原有的渲染邏輯（向後兼容）
        this.renderDefaultBag();
    }

    // 渲染默認背包（向後兼容）
    renderDefaultBag() {
        console.log("渲染默認背包");

        // 找到背包項目容器
        const bagContainer = document.getElementById("playerBagContainer");
        if (!bagContainer) {
            console.warn("找不到背包容器");
            return;
        }

        // 找到背包項目容器（playerBagItemsContainer）
        const bagItemsContainer = bagContainer.querySelector('div[style*="margin-left: 6%"]');
        if (!bagItemsContainer) {
            console.warn("找不到背包項目容器");
            return;
        }

        // 清空背包項目容器
        bagItemsContainer.innerHTML = '';

        // 重新構建背包內容（使用原有樣式）
        const itemMap = new Map();

        // 統計 mybag 中的物品數量和價格
        if (mybag && mybag.length > 0) {
            mybag.forEach(item => {
                if (itemMap.has(item.name)) {
                    const existingData = itemMap.get(item.name);
                    existingData.count += 1;
                    itemMap.set(item.name, existingData);
                } else {
                    itemMap.set(item.name, {
                        count: 1,
                        price: item.price || 0,
                        item: item
                    });
                }
            });
        }

        // 顯示背包物品（使用原有樣式）
        if (itemMap.size > 0) {
            itemMap.forEach((data, name) => {
                const itemDiv = document.createElement("div");
                itemDiv.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 20px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    font-size: 24px;
                    color: rgb(168, 105, 38);
                    box-sizing: border-box;
                    border: 3px solid transparent;
                `;

                itemDiv.innerHTML = `
                    <span style="font-weight: bold; color: rgb(168, 105, 38);">${name} 【${data.count}件】</span>
                    <span style="color: #B8860B; font-weight: bold;">🪙 ${data.price / 2}</span>
                `;

                itemDiv.addEventListener('mouseenter', () => {
                    itemDiv.style.borderColor = 'brown';
                });

                itemDiv.addEventListener('mouseleave', () => {
                    itemDiv.style.borderColor = 'transparent';
                });

                // 添加點擊事件來賣出物品（默認背包，一次只賣一件）
                itemDiv.addEventListener('click', () => {
                    this.showSellConfirmDialog(data.item, 1, name);
                });

                bagItemsContainer.appendChild(itemDiv);
            });
        } else {
            const emptyMsg = document.createElement("div");
            emptyMsg.textContent = "背包是空的";
            emptyMsg.style.cssText = `
                color: #999;
                text-align: center;
                font-style: italic;
                padding: 20px;
                font-size: 14px;
            `;
            bagItemsContainer.appendChild(emptyMsg);
        }

        console.log("背包顯示已更新，物品數量:", itemMap.size);
    }

    // 關閉商店
    closeShop(shopDialog) {
        console.log("關閉商店");

        // 清理 Esc 鍵事件監聽器
        if (shopDialog.escapeHandler) {
            document.removeEventListener('keydown', shopDialog.escapeHandler);
            shopDialog.escapeHandler = null;
            console.log("已清理商店 Esc 鍵事件監聽器");
        }

        // 關閉對話框
        shopDialog.close();
        document.body.removeChild(shopDialog);

        // 恢復鍵盤控制
        this.resumeKeyboardControls();

        console.log("商店已關閉，鍵盤控制已恢復");
    }

    // 設置音樂
    setupMusic() {
        if (this.mapData.bgm) {
            // 停止之前的音樂
            if (window.bgmAudio) {
                window.bgmAudio.pause();
                window.bgmAudio.currentTime = 0;
            }

            window.bgmAudio = new Audio();
            window.bgmAudio.src = this.mapData.bgm;
            window.bgmAudio.loop = true;

            // 自動播放（如果用戶已經與頁面互動過）
            window.bgmAudio.play().catch(error => {
                console.log("音樂自動播放失敗，等待用戶互動:", error);
            });

            // 監聽音樂結束事件
            window.bgmAudio.addEventListener('ended', () => {
                console.log("營地背景音樂播放完成");
            });
        }
    }

    // 設置鏡頭
    setupCamera() {
        // 初始化鏡頭位置
        this.cameraX = 0;
        this.cameraY = 0;
        console.log("鏡頭設置完成");
    }

    // 開始遊戲循環
    startGameLoop() {
        console.log("開始營地場景遊戲循環");

        // 檢查是否完全載入
        if (!this.isLoaded || !this.character) {
            console.warn("場景或角色尚未完全載入，延遲啟動遊戲循環");
            setTimeout(() => this.startGameLoop(), 100);
            return;
        }

        this.isRunning = true;
        this.lastTime = performance.now();

        // 立即渲染一次
        this.render();

        // 開始遊戲循環
        this.gameLoop(this.lastTime);

    }

    // 遊戲循環（使用 requestAnimationFrame 和 delta time）
    gameLoop(currentTime) {
        if (!this.isRunning) return;

        // 計算 delta time
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;

        // 處理輸入
        this.handleInput(deltaTime);

        // 更新遊戲狀態
        this.update(deltaTime);

        // 渲染
        this.render();

        // 繼續循環
        this.animationId = requestAnimationFrame((time) => this.gameLoop(time));
    }

    // 處理輸入（基於像素移動）
    handleInput(deltaTime) {
        if (!this.character || !this.isLoaded || this.keyboardPaused) return;

        let moveX = 0;
        let moveY = 0;
        let newDirection = null;

        // 檢查按鍵
        if (this.keyPressed['ArrowUp'] || this.keyPressed['KeyW']) {
            moveY = -1;
            newDirection = 'up';
        } else if (this.keyPressed['ArrowDown'] || this.keyPressed['KeyS']) {
            moveY = 1;
            newDirection = 'down';
        }

        if (this.keyPressed['ArrowLeft'] || this.keyPressed['KeyA']) {
            moveX = -1;
            newDirection = 'left';
        } else if (this.keyPressed['ArrowRight'] || this.keyPressed['KeyD']) {
            moveX = 1;
            newDirection = 'right';
        }

        // 如果有移動輸入
        if (moveX !== 0 || moveY !== 0) {
            // 計算移動距離（像素）
            const moveDistance = this.character.speed * deltaTime / 1000;
            const targetX = this.character.x + moveX * moveDistance;
            const targetY = this.character.y + moveY * moveDistance;

            // 檢查碰撞
            if (!this.character.checkCollision(targetX, targetY, this.mapData.obstacles || [], this.mapCols, this.mapRows)) {
                // 更新角色位置
                this.character.x = targetX;
                this.character.y = targetY;
                this.character.direction = newDirection;

                // 設置走路狀態
                if (this.character.state !== `walk-${newDirection}`) {
                    this.character.setState(`walk-${newDirection}`);
                }

                // 更新鏡頭
                this.updateCamera();

                // 檢查觸發點
                this.checkTriggerPoints();
            }
        } else {
            // 沒有移動輸入，切換為站立狀態
            if (this.character.state.startsWith('walk-')) {
                this.character.setState(`idle-${this.character.direction}`);
            }
        }
    }

    // 更新遊戲狀態
    update(deltaTime) {
        if (!this.character || !this.isLoaded) return;

        // 更新角色
        this.character.update(deltaTime);
    }

    // 更新鏡頭
    updateCamera() {
        if (!this.character) return;

        // 將鏡頭對準角色
        const gameMap = document.getElementById('GameMap');
        if (gameMap) {
            //使用clientWidth和clientHeight來取得可視區域的大小
            gameMap.scrollLeft = this.character.x - gameMap.clientWidth / 2;
            gameMap.scrollTop = this.character.y - gameMap.clientHeight / 2;
        }
    }

    // 檢查觸發點
    checkTriggerPoints() {
        if (!this.character) return;

        const gridPos = this.character.getGridPosition();

        for (const trigger of this.triggerPoints) {
            if (trigger.position === gridPos.position) {
                console.log("觸發點被激活:", trigger);
                this.handleTrigger(trigger);
                break;
            }
        }
    }





    // 處理觸發事件
    handleTrigger(trigger) {
        switch (trigger.type) {
            case 'nextLevel':
                console.log(`觸發進入下一關: ${trigger.levelIndex}`);

                // 觸發營地觸發事件
                const campTriggerEvent = new CustomEvent('campTrigger', {
                    detail: {
                        type: 'nextLevel',
                        levelIndex: trigger.levelIndex
                    }
                });
                document.dispatchEvent(campTriggerEvent);
                break;

            case 'shop':
                console.log("觸發商店");
                this.openShop(trigger);
                break;

            case 'hostel':
                console.log("觸發客棧");
                this.openHostel(trigger);
                break;

            case 'dialogue':
                console.log("觸發對話");
                // 可以實現對話邏輯
                break;

            default:
                console.log("未知觸發類型:", trigger.type);
        }
    }

    // 開啟客棧
    openHostel(trigger) {
        console.log("開啟客棧:", trigger);

        // 1. 暫停鍵盤事件監聽
        this.pauseKeyboardControls();

        // 2. 移動玩家到客棧外一格
        this.movePlayerOutsideHostel(trigger.position);

        // 3. 切換到客棧場景
        this.switchToHostelScene();
    }

    // 移動玩家到客棧外一格
    movePlayerOutsideHostel(hostelPosition) {
        console.log("移動玩家到客棧外一格");

        if (!this.character) return;

        // 計算客棧外一格的位置（假設在客棧左邊一格）
        const outsidePosition = hostelPosition - 1;
        const gridX = outsidePosition % this.mapCols;
        const gridY = Math.floor(outsidePosition / this.mapCols);

        // 移動角色到客棧外
        this.character.setGridPosition(gridX, gridY);
        console.log(`玩家移動到客棧外位置: ${outsidePosition} (${gridX}, ${gridY})`);
    }

    // 切換到客棧場景
    switchToHostelScene() {
        console.log("切換到客棧場景");

        // 使用場景管理器切換到客棧
        if (typeof sceneManager !== 'undefined' && typeof sceneManager.switchToHostel === 'function') {
            const hostelIndex = this.campIndex; // 使用相同的索引
            sceneManager.switchToHostel(hostelIndex, 'camp');
            console.log("客棧場景切換已觸發");
        } else {
            console.error("sceneManager 或 switchToHostel 方法不可用");
            // 恢復鍵盤控制作為備用方案
            this.resumeKeyboardControls();
        }
    }

    // 渲染
    render() {
        if (!this.ctx) {
            console.warn("Canvas context 不存在，無法渲染");
            return;
        }

        // 清除整個畫布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 保存當前狀態
        this.ctx.save();

        // 繪製背景
        this.drawBackground();

        // 繪製格子（調試用）
        this.drawGrid();

        // 繪製觸發點（調試用）
        this.drawTriggerPoints();

        // 恢復狀態
        this.ctx.restore();



        // 繪製角色（只有在完全載入且角色存在時）
        if (this.character && this.isLoaded && this.character.imagesLoaded) {
            try {
                this.character.draw(this.ctx);
            } catch (error) {
                console.warn("角色繪製錯誤:", error);
                // 繪製備用矩形
                this.ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
                this.ctx.fillRect(
                    this.character.x || 0,
                    this.character.y || 0,
                    this.cellWidth * 0.8,
                    this.cellHeight * 0.8
                );
            }
        } else if (this.character && !this.character.imagesLoaded) {
            // 圖片還在載入中，顯示載入指示器
            this.ctx.fillStyle = 'rgba(0, 0, 255, 0.5)';
            this.ctx.fillRect(
                this.character.x || 0,
                this.character.y || 0,
                this.cellWidth * 0.8,
                this.cellHeight * 0.8
            );

            this.ctx.fillStyle = 'white';
            this.ctx.font = '12px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(
                '載入中...',
                (this.character.x || 0) + this.cellWidth / 2,
                (this.character.y || 0) + this.cellHeight / 2
            );
        }

        // 調試資訊（最上層）
        this.drawDebugInfo();

        this.updateCamera()
    }

    // 繪製調試資訊
    drawDebugInfo() {
        if (!this.ctx) return;

        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(10, 10, 350, 140);

        this.ctx.fillStyle = 'white';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'left';

        const debugInfo = [
            `場景載入: ${this.isLoaded ? '是' : '否'}`,
            `Canvas: ${this.canvas.width}x${this.canvas.height}`,
            `網格: ${this.mapCols}x${this.mapRows}`,
            `角色實例: ${this.character ? '存在' : '不存在'}`
        ];

        // 只有當角色存在且完全初始化時才顯示角色資訊
        if (this.character && this.character.x !== undefined && this.character.y !== undefined) {
            debugInfo.push(
                `角色位置: (${Math.round(this.character.x)}, ${Math.round(this.character.y)})`,
                `狀態: ${this.character.state}`,
                `方向: ${this.character.direction}`,
                `移動中: ${this.character.isMoving ? '是' : '否'}`,
                `當前幀: ${this.character.currentFrame}`,
                `圖片已載入: ${this.character.imagesLoaded ? '是' : '否'}`
            );

            // 安全地獲取格子位置
            try {
                const gridPos = this.character.getGridPosition();
                debugInfo.push(`格子位置: (${gridPos.x}, ${gridPos.y})`);
            } catch (error) {
                debugInfo.push(`格子位置: 計算錯誤`);
            }
        } else {
            debugInfo.push(`角色狀態: 初始化中...`);
        }

        debugInfo.forEach((info, index) => {
            this.ctx.fillText(info, 15, 25 + index * 12);
        });
    }



    // 繪製背景
    drawBackground() {
        if (this.backgroundImg) {
            // 如果有背景圖片，繪製背景圖片
            this.ctx.drawImage(this.backgroundImg, 0, 0, this.canvas.width, this.canvas.height);
        } else {
            // 如果沒有背景圖片，繪製簡單的漸變背景
            const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
            gradient.addColorStop(0, '#87CEEB'); // 天空藍
            gradient.addColorStop(0.7, '#98FB98'); // 淺綠色
            gradient.addColorStop(1, '#228B22'); // 森林綠

            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

            // 添加一些裝飾性元素
            this.drawDefaultBackground();
        }
    }

    // 繪製預設背景裝飾
    drawDefaultBackground() {
        this.ctx.save();

        // 繪製一些雲朵
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        for (let i = 0; i < 5; i++) {
            const x = (i * this.canvas.width / 5) + Math.sin(Date.now() / 2000 + i) * 50;
            const y = 50 + Math.cos(Date.now() / 3000 + i) * 20;
            this.drawCloud(x, y, 60);
        }

        // 繪製一些樹木
        this.ctx.fillStyle = '#8B4513'; // 棕色樹幹
        for (let i = 0; i < 8; i++) {
            const x = (i * this.canvas.width / 8) + 50;
            const y = this.canvas.height - 200;
            this.drawTree(x, y);
        }

        this.ctx.restore();
    }

    // 繪製雲朵
    drawCloud(x, y, size) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, size * 0.5, 0, Math.PI * 2);
        this.ctx.arc(x + size * 0.3, y, size * 0.4, 0, Math.PI * 2);
        this.ctx.arc(x - size * 0.3, y, size * 0.4, 0, Math.PI * 2);
        this.ctx.arc(x + size * 0.1, y - size * 0.3, size * 0.3, 0, Math.PI * 2);
        this.ctx.fill();
    }

    // 繪製樹木
    drawTree(x, y) {
        // 樹幹
        this.ctx.fillStyle = '#8B4513';
        this.ctx.fillRect(x - 5, y, 10, 40);

        // 樹葉
        this.ctx.fillStyle = '#228B22';
        this.ctx.beginPath();
        this.ctx.arc(x, y - 10, 25, 0, Math.PI * 2);
        this.ctx.fill();
    }

    // 繪製格子（調試用）
    drawGrid() {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;

        // 繪製垂直線
        for (let x = 0; x <= this.mapCols; x++) {
            this.ctx.beginPath();
            this.ctx.moveTo(x * this.cellWidth - this.cameraX, 0 - this.cameraY);
            this.ctx.lineTo(x * this.cellWidth - this.cameraX, this.mapRows * this.cellHeight - this.cameraY);
            this.ctx.stroke();
        }

        // 繪製水平線
        for (let y = 0; y <= this.mapRows; y++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0 - this.cameraX, y * this.cellHeight - this.cameraY);
            this.ctx.lineTo(this.mapCols * this.cellWidth - this.cameraX, y * this.cellHeight - this.cameraY);
            this.ctx.stroke();
        }

        // 繪製格子座標數字
        this.drawGridCoordinates();
    }

    // 繪製格子座標數字
    drawGridCoordinates() {
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.textBaseline = 'top';

        for (let y = 0; y < this.mapRows; y++) {
            for (let x = 0; x < this.mapCols; x++) {
                const position = y * this.mapCols + x;
                const drawX = x * this.cellWidth - this.cameraX + 2;
                const drawY = y * this.cellHeight - this.cameraY + 2;

                // 只在可見區域繪製座標
                if (drawX > -50 && drawX < this.canvas.width + 50 &&
                    drawY > -50 && drawY < this.canvas.height + 50) {
                    this.ctx.fillText(position.toString(), drawX, drawY);
                }
            }
        }
    }



    // 繪製觸發點（調試用）
    drawTriggerPoints() {
        for (const trigger of this.triggerPoints) {
            const gridX = trigger.position % this.mapCols;
            const gridY = Math.floor(trigger.position / this.mapCols);

            const drawX = gridX * this.cellWidth - this.cameraX;
            const drawY = gridY * this.cellHeight - this.cameraY;

            // 根據觸發類型使用不同顏色
            let color = 'yellow';
            switch (trigger.type) {
                case 'nextLevel':
                    color = 'green';
                    break;
                case 'shop':
                    color = 'gold';
                    break;
                case 'dialogue':
                    color = 'cyan';
                    break;
            }

            this.ctx.fillStyle = color;
            this.ctx.globalAlpha = 0.5;
            this.ctx.fillRect(drawX, drawY, this.cellWidth, this.cellHeight);
            this.ctx.globalAlpha = 1.0;

            // 繪製觸發點標記
            this.ctx.fillStyle = 'black';
            this.ctx.font = '12px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(
                trigger.type,
                drawX + this.cellWidth / 2,
                drawY + this.cellHeight / 2
            );
        }
    }

    // 停止場景
    stop() {
        console.log("停止營地場景");
        this.isRunning = false;

        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }

        // 停止音樂
        if (window.bgmAudio) {
            window.bgmAudio.pause();
            window.bgmAudio.currentTime = 0;
        }

        // 移除鍵盤事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
            this.keydownHandler = null;
        }
        if (this.keyupHandler) {
            document.removeEventListener('keyup', this.keyupHandler);
            this.keyupHandler = null;
        }

        console.log("營地場景事件監聽器已移除");
    }

    // 清理資源
    cleanup() {
        this.stop();

        // 清理 NPC
        this.cleanupNPCs();

        // 恢復 sidebar 按鈕狀態
        this.enableSaveLoadButtons();

        // 清理Canvas
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
        }

        console.log("營地場景清理完成");
    }
}

// 營地資料定義
let campdata = [
    {
        id: "第零幕 獨行",
        map: "./Public/Camp/0/map.png",
        bgm: "./Public/Camp/0/bgm.mp3",
        width: "100%",
        height: "200%",
        size: {
            cols: 34,
            rows: 33
        },
        role: { id: "player1", Position: 363 },
        npcs: [
            {
                id: "shopkeeper",
                name: "",
                position: 75,
                type: "dialog",
                shopName: "營地商店",
                image: "./Public/Camp/0/npc/0.gif",
                width: 330,
                height: 500,
                dialog: "霍~霍~霍~"
            },
            {
                id: "younger",
                name: "",
                position: 335,
                type: "dialog",
                image: "./Public/Camp/0/npc/1.gif",
                width: 100,
                height: 170,
                dialog: "滾！"
            },
            {
                id: "child",
                name: "",
                position: 489,
                type: "dialog",
                image: "./Public/Camp/0/npc/3.gif",
                width: 150,
                height: 200,
                dialog: "哈哈！"
            },
            {
                id: "women",
                name: "",
                position: 333,
                type: "dialog",
                image: "./Public/Camp/0/npc/2.gif",
                width: 180,
                height: 230,
                dialog: "機哩瓜啦~"
            }
        ],
        obstacles: [
            144, 177, 211, 246, 281, 282, 283, 284, 285, 286, 287, 288,
            323, 358, 359, 393, 427, 426, 460, 493, 488, 489, 490, 491,
            492, 646, 647, 648, 649, 616, 617, 618, 585, 586, 587, 553,
            519, 485, 453, 451, 452, 111, 112, 79, 80, 81, 82, 116, 151,
            186, 221, 222, 223, 224, 250, 192, 193, 160, 161, 162, 163,
            164, 198, 232, 266, 299, 300, 301, 302, 337, 371, 372, 373,
            435, , 470, 471, 472, 473, 474, 475, 467, 468, 500, 532, 533,
            531, 564, 597, 631, 664, 663, 466, 499, 498, 434, 225, 523,
            782, 783, 751, 750, 752, 753, 788, 789, 790, 757, 758, 759,
            760, 756, 795, 796, 797, 798, 799, 800, 801, 836, 837, 872,
            874, 873, 910, 909, 911, 912, 947, 948, 949, 984, 985, 664,
            665, 666, 667, 668, 669, 670, 671, 672, 673, 707, 741, 776,
            811, 846, 847, 848, 849, 333, 367, 368, 335, 332, 489, 490,
            524
        ],
        triggerPoints: [
            {
                position: 197,
                type: 'nextLevel',
                levelIndex: 1,
                name: '進入第壹幕'
            },
            {
                position: 196,
                type: 'nextLevel',
                levelIndex: 1,
                name: '進入第壹幕'
            },
            {
                position: 115,
                type: 'shop',
                name: '鐵匠鋪'
            },
            {
                position: 114,
                type: 'shop',
                name: '鐵匠鋪'
            },
            {
                position: 116,
                type: 'shop',
                name: '鐵匠鋪'
            },
            {
                position: 486,
                type: 'shop',
                name: '雜貨店'
            },
            {
                position: 706,
                type: 'shop',
                name: '藥鋪'
            },
            {
                position: 705,
                type: 'shop',
                name: '藥鋪'
            },
            {
                position: 742,
                type: 'shop',
                name: '藥鋪'
            },
            {
                position: 117,
                type: 'hostel',
                name: '客棧'
            }

        ]
    },
    {
        id: "第壹幕 因緣",
        map: "./Public/Camp/0/map.png", // 暫時使用同一張圖
        bgm: "./Public/Camp/0/bgm.mp3", // 暫時使用同一個音樂
        width: "150%",
        height: "200%",
        size: {
            cols: 29,
            rows: 20
        },
        role: { id: "player1", Position: 100 },
        npcs: [
            {
                id: "blacksmith",
                name: "鐵匠",
                position: 115,
                type: "shop",
                shopName: "鐵匠鋪",
                image: "./Public/NPC/blacksmith.png",
                width: 80,
                height: 120,
                dialog: "歡迎來到鐵匠鋪！我這裡有最好的武器和防具。"
            },
            {
                id: "merchant",
                name: "商人",
                position: 486,
                type: "shop",
                shopName: "雜貨店",
                image: "./Public/NPC/merchant.png",
                width: 80,
                height: 120,
                dialog: "需要什麼雜貨嗎？我這裡應有盡有！"
            },
            {
                id: "guard",
                name: "守衛",
                position: 197,
                type: "dialog",
                image: "./Public/NPC/guard.png",
                width: 80,
                height: 120,
                dialog: "前方就是戰場了，小心一點！"
            }
        ],
        obstacles: [

        ],
        triggerPoints: [
            {
                position: 150,
                type: 'nextLevel',
                levelIndex: 2,
                name: '進入第貳幕'
            },
            {
                position: 80,
                type: 'shop',
                name: '高級武器商店'
            }
        ]
    }
];

// 將關閉說明對話框函數設為全局可用
window.closeInstructionDialog = function() {
    const dialogs = document.querySelectorAll('dialog');
    dialogs.forEach(dialog => {
        if (dialog.innerHTML.includes('營地操作說明')) {
            dialog.close();
            document.body.removeChild(dialog);
            console.log("遊戲說明對話框已關閉");
        }
    });
};

