//敵人可以穿過敵人移動但不能站在其他敵人的位置上且會被player擋住，並且不能經過玩家上下左右的位置，但最後還會補回合法的玩家上下左右位置(若能到達才補回來) - Canvas版本
function bfsforenemy(startPosition, moveRange, enemyIndex) {
    console.log(`計算敵人 ${enemyIndex} 從位置 ${startPosition} 的移動範圍，移動力: ${moveRange}`);

    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    const queue = [{ position: startPosition, distance: 0 }];
    const visited = new Set();
    const finalPositions = new Set();
    visited.add(startPosition);

    // 用於存儲所有可到達的格子及其距離
    const reachableCells = new Map();
    reachableCells.set(startPosition, 0);

    // 清除之前的敵人移動高亮
    clearEnemyMoveHighlights();

    // 獲取所有玩家的位置
    const playerPositions = gameplayers.map(player => player.Position);
    // 獲取所有玩家相鄰的位置
    const playerAdjacentPositions = new Set();
    playerPositions.forEach(playerPos => {
        directions.forEach(dir => {
            const newX = (playerPos % controlLayer[currentLevel].size.cols) + dir.x;
            const newY = Math.floor(playerPos / controlLayer[currentLevel].size.cols) + dir.y;
            if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                playerAdjacentPositions.add(newY * controlLayer[currentLevel].size.cols + newX);
            }
        });
    });

    // 獲取所有其他敵人的位置（不包括當前敵人）
    const otherEnemyPositions = gameenemys
        .filter((enemy, index) => index !== enemyIndex && enemy.CurHP > 0)
        .map(enemy => enemy.Position);

    while (queue.length > 0) {
        const { position, distance } = queue.shift();

        if (distance < moveRange) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                // 檢查邊界
                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    // 檢查是否已經訪問過
                    if (visited.has(newPosition)) {
                        return;
                    }

                    // 檢查是否是障礙物
                    if (gameobstacles.includes(newPosition)) {
                        return;
                    }

                    // 檢查是否是玩家位置
                    if (playerPositions.includes(newPosition)) {
                        return;
                    }

                    // 檢查是否是玩家相鄰位置
                    if (playerAdjacentPositions.has(newPosition)) {
                        // 先標記為已訪問，但不加入隊列
                        visited.add(newPosition);
                        return;
                    }

                    // 如果通過所有檢查，加入隊列
                    visited.add(newPosition);
                    const newDistance = distance + 1;
                    queue.push({ position: newPosition, distance: newDistance });
                    reachableCells.set(newPosition, newDistance);
                }
            });
        }
    }

    // 第二遍檢查：如果能夠到達玩家相鄰位置，則加入最終位置集合
    for (let pos of visited) {
        // 檢查該位置是否被其他敵人佔據
        if (!otherEnemyPositions.includes(pos)) {
            if (playerAdjacentPositions.has(pos)) {
                // 檢查是否有路徑可以到達這個位置，並計算實際路徑距離
                let minDistance = Infinity;
                directions.forEach(dir => {
                    const posRow = Math.floor(pos / controlLayer[currentLevel].size.cols);
                    const posCol = pos % controlLayer[currentLevel].size.cols;
                    const newRow = posRow + dir.y;
                    const newCol = posCol + dir.x;
                    if (newRow >= 0 && newRow < controlLayer[currentLevel].size.rows &&
                        newCol >= 0 && newCol < controlLayer[currentLevel].size.cols) {
                        const adjacentPos = newRow * controlLayer[currentLevel].size.cols + newCol;
                        if (reachableCells.has(adjacentPos)) {
                            const distance = reachableCells.get(adjacentPos);
                            minDistance = Math.min(minDistance, distance + 1);
                        }
                    }
                });

                // 只有當實際路徑距離在移動範圍內時，才添加到最終位置
                if (minDistance <= moveRange) {
                    finalPositions.add(pos);
                }
            } else {
                finalPositions.add(pos);
            }
        }
    }

    // 敵人移動時不顯示移動範圍，只返回計算結果
    console.log(`敵人 ${enemyIndex} 移動範圍計算完成，共 ${finalPositions.size} 個可移動位置`);
    return finalPositions;
}

// 檢查是否有路徑可以到達指定位置
function checkPathToPosition(startPos, targetPos, maxDistance) {
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    const queue = [{ position: startPos, distance: 0 }];
    const visited = new Set();
    visited.add(startPos);

    while (queue.length > 0) {
        const { position, distance } = queue.shift();

        if (position === targetPos) {
            return true;
        }

        if (distance < maxDistance) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    if (
                        !visited.has(newPosition) &&
                        !gameobstacles.includes(newPosition) &&
                        !gameplayers.some(player => player.Position === newPosition)
                    ) {
                        visited.add(newPosition);
                        queue.push({ position: newPosition, distance: distance + 1 });
                    }
                }
            });
        }
    }

    return false;
}

const ENEMY_MOVE_COLOR = 'rgba(255, 0, 0, 0.92)';

// 清除敵人移動高亮的函數
function clearEnemyMoveHighlights() {
    // 移除所有敵人移動相關的高亮
    highlightCells = highlightCells.filter(cell =>
        cell.color !== ENEMY_MOVE_COLOR
    );
    render();
}

// 敵人方向計算函數 - 參考players.js的實現
function getEnemyDirection(fromRow, fromCol, toRow, toCol) {
    if (fromRow < toRow) return 0; // down
    if (fromRow > toRow) return 1; // up
    if (fromCol < toCol) return 2; // right
    if (fromCol > toCol) return 3; // left
    return -1; // 無效方向
}

function getEnemyDirectionName(directionIndex) {
    const directions = ['down', 'up', 'right', 'left'];
    return directions[directionIndex] || null;
}

// 停止敵人站立動畫
function stopEnemyStandAnimation(enemyObj) {
    if (enemyObj && enemyObj.isStandAnimating) {
        enemyObj.isStandAnimating = false;
        if (enemyObj.animationId) {
            cancelAnimationFrame(enemyObj.animationId);
            enemyObj.animationId = null;
        }
    }
}

// 恢復敵人站立動畫
function resumeEnemyStandAnimation(enemyObj) {
    if (enemyObj && enemyObj.standImages && enemyObj.standImages.length > 1) {
        // 重置到第一幀
        enemyObj.currentFrameIndex = 0;
        startStandAnimation(enemyObj);
    }
}

// 設置敵人站立圖片
function setEnemyStandImage(enemy, enemyIndex, position) {
    // 使用記錄的最後移動方向
    let lastDirection = enemy.lastdirect || 'down'; // 使用 lastdirect 屬性，預設為 down

    console.log(`敵人 ${enemy.name} 最後移動方向: ${lastDirection}`);

    // 獲取對應方向的站立圖片
    let standImages = enemy.Stand && enemy.Stand[lastDirection];
    if (!standImages || !Array.isArray(standImages) || standImages.length === 0) {
        // 如果沒有對應方向的站立圖片，使用預設圖片
        console.warn(`敵人 ${enemy.name} 沒有 ${lastDirection} 方向的站立圖片，使用 down 方向`);
        standImages = enemy.Stand && enemy.Stand['down'];
        if (!standImages) {
            console.warn(`敵人 ${enemy.name} 沒有站立圖片`);
            return;
        }
    }

    // 找到敵人物件並更新其站立圖片陣列
    let enemyObj = mapObjects.find(obj =>
        obj.type === 'enemy' && obj.enemyIndex === enemyIndex
    );

    if (enemyObj) {
        // 更新站立圖片陣列為新方向
        enemyObj.standImages = standImages;
        enemyObj.currentFrameIndex = 0;

        // 使用第一張站立圖片
        let standImageSrc = standImages[0];

        console.log(`設置敵人 ${enemy.name} 站立圖片: ${standImageSrc}`);

        // 更新敵人物件的圖片和位置
        updateEnemyPositionInMapObjects(enemyIndex, position, standImageSrc);
    }
}

// 平滑移動到下一個格子的函數 - 參考players.js的實現
function smoothMoveEnemyToNextCell(fromPos, toPos, enemy, enemyIndex, direction, duration, onComplete) {
    const startTime = performance.now();

    // 計算起始和目標座標
    const fromCol = fromPos % controlLayer[currentLevel].size.cols;
    const fromRow = Math.floor(fromPos / controlLayer[currentLevel].size.cols);
    const toCol = toPos % controlLayer[currentLevel].size.cols;
    const toRow = Math.floor(toPos / controlLayer[currentLevel].size.cols);

    // 計算像素座標
    const startX = fromCol * cellWidth;
    const startY = fromRow * cellHeight;
    const endX = toCol * cellWidth;
    const endY = toRow * cellHeight;

    // 獲取移動圖片
    let moveImages = enemy.MoveRes[direction];
    let animationFrameIndex = 0;

    function animate(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1); // 0 到 1 的進度

        // 使用緩動函數讓移動更自然
        const easeProgress = easeInOutQuadEnemy(progress);

        // 計算當前位置
        const currentX = startX + (endX - startX) * easeProgress;
        const currentY = startY + (endY - startY) * easeProgress;

        // 選擇當前動畫幀
        let currentImage;
        if (Array.isArray(moveImages)) {
            // 根據時間循環播放動畫幀
            const frameIndex = Math.floor((elapsed / 100)) % moveImages.length; // 每100ms換一幀
            currentImage = moveImages[frameIndex];
        } else if (typeof moveImages === 'string') {
            currentImage = moveImages;
        }

        // 更新敵人在mapObjects中的位置和圖片
        updateEnemySmoothPosition(enemyIndex, currentX, currentY, currentImage);

        // 重新渲染
        render();

        // 檢查動畫是否完成
        if (progress < 1) {
            requestAnimationFrame(animate);
        } else {
            // 動畫完成，確保敵人位置精確
            updateEnemySmoothPosition(enemyIndex, endX, endY, currentImage);
            render();
            onComplete();
        }
    }

    requestAnimationFrame(animate);
}

// 緩動函數 - 讓移動更自然
function easeInOutQuadEnemy(t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
}

// 更新敵人平滑移動位置的函數
function updateEnemySmoothPosition(enemyIndex, pixelX, pixelY, imageSrc) {
    // 找到對應的敵人物件
    let enemyObj = mapObjects.find(obj =>
        obj.type === 'enemy' && obj.enemyIndex === enemyIndex
    );

    if (!enemyObj) {
        console.warn(`找不到敵人 ${enemyIndex} 的物件`);
        return;
    }

    // 載入新圖片
    if (imageSrc) {
        preloadImage(imageSrc).then(img => {
            enemyObj.img = img;
        }).catch(error => {
            console.warn(`載入敵人移動圖片失敗: ${imageSrc}`, error);
        });
    }

    // 設置自定義位置
    enemyObj.useCustomPosition = true;
    enemyObj.customX = pixelX;
    enemyObj.customY = pixelY;
}

// 更新敵人位置在mapObjects中的函數
function updateEnemyPositionInMapObjects(enemyIndex, position, imageSrc) {
    let enemyObj = mapObjects.find(obj =>
        obj.type === 'enemy' && obj.enemyIndex === enemyIndex
    );

    if (enemyObj) {
        // 更新格子位置
        enemyObj.gridX = position % controlLayer[currentLevel].size.cols;
        enemyObj.gridY = Math.floor(position / controlLayer[currentLevel].size.cols);
        enemyObj.useCustomPosition = false;

        // 載入新圖片
        if (imageSrc) {
            preloadImage(imageSrc).then(img => {
                enemyObj.img = img;
                render();
            }).catch(error => {
                console.warn(`載入敵人圖片失敗: ${imageSrc}`, error);
            });
        }
    }
}

function DrawEnemyMoveRange(startPosition, enemyIndex, moveRange) {
    // 先清除舊高亮
    clearAllEnemyHighlights();

    const enemyCanMove = bfsforenemy(startPosition, moveRange, enemyIndex);

    enemyCanMove.forEach(pos => {
        if (pos === startPosition) return; // 不高亮原地

        // 加入全域 highlightCells，避免重複
        if (typeof highlightCells !== 'undefined') {
            const x = pos % controlLayer[currentLevel].size.cols;
            const y = Math.floor(pos / controlLayer[currentLevel].size.cols);
            highlightCells.push({ x, y, color: ENEMY_MOVE_COLOR });
        }
    });

    if (typeof render === 'function') {
        render();
    }
}

function clearAllEnemyHighlights() {
    if (typeof highlightCells !== 'undefined' && highlightCells.length) {
        highlightCells = highlightCells.filter(c => c.color !== ENEMY_MOVE_COLOR);
        if (typeof render === 'function') render();
    }
}

function enemyfindpath(startPosition, targetPosition, moveRange, enemyIndex) {
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    const queue = [{ position: startPosition, distance: 0, path: [startPosition] }];
    const visited = new Set();
    visited.add(startPosition);

    while (queue.length > 0) {
        const { position, distance, path } = queue.shift();

        if (position === targetPosition) {
            return path;
        }

        if (distance < moveRange) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                // Check boundaries
                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    if (
                        !visited.has(newPosition) &&
                        !gameobstacles.includes(newPosition) &&
                        !gameplayers.some(player => player.Position === newPosition)
                    ) {
                        visited.add(newPosition);
                        queue.push({ position: newPosition, distance: distance + 1, path: [...path, newPosition] });
                    }
                }
            });
        }
    }

    return [];
}

// 重寫的animateEnemyMove函數 - 完全參考animatePlayerMove的實現
function animateEnemyMove(path, enemyIndex) {
    if (path.length === 0) {
        console.warn("敵人移動路徑為空，無法進行移動動畫");
        return;
    }

    console.log(`開始敵人 ${enemyIndex} 移動動畫，路徑長度: ${path.length}`);

    let currentPathIndex = 0;
    let enemy = gameenemys[enemyIndex];
    if (enemy.CurHP <= 0) {
        console.warn(`敵人 ${enemyIndex} 已死亡，取消移動動畫`);
        return;
    }

    let stepaudio = operates.playSound("./Public/footstepsound.mp3", { loop: true });
    if (!stepaudio) {
        console.warn("播放敵人移動音效失敗");
    }

    // 停止敵人的站立動畫
    let enemyObj = mapObjects.find(obj =>
        obj.type === 'enemy' && obj.enemyIndex === enemyIndex
    );
    if (enemyObj) {
        stopEnemyStandAnimation(enemyObj);
    }

    // 動畫參數
    const moveSpeed = 380; // 每步移動時間(毫秒)
    const frameRate = 16; // 約60fps (1000/60 ≈ 16ms)

    function animateStep() {
        // 檢查是否到達路徑終點
        if (currentPathIndex >= path.length - 1) {
            // 敵人到達目的地
            let finalPos = path[currentPathIndex];
            enemy.Position = finalPos;

            // 停止移動音效（如果存在）
            if (stepaudio) {
                stepaudio.pause();
            }

            clearEnemyMoveHighlights();

            // 移動完成後使用 Stand 圖片
            setEnemyStandImage(enemy, enemyIndex, finalPos);

            updateMapObjects(); // 重新更新所有物件到最終位置
            render(); // 重新渲染

            // 重新啟動站立動畫
            let enemyObj = mapObjects.find(obj =>
                obj.type === 'enemy' && obj.enemyIndex === enemyIndex
            );
            if (enemyObj) {
                resumeEnemyStandAnimation(enemyObj);
            }

            console.log(`敵人 ${enemyIndex} 移動完成，到達位置 ${finalPos}`);
            return;
        }

        let currentPos = path[currentPathIndex];
        let nextPos = path[currentPathIndex + 1];

        // 計算當前和下一個位置的行列座標
        let rowCurrent = Math.floor(currentPos / controlLayer[currentLevel].size.cols);
        let colCurrent = currentPos % controlLayer[currentLevel].size.cols;
        let rowNext = Math.floor(nextPos / controlLayer[currentLevel].size.cols);
        let colNext = nextPos % controlLayer[currentLevel].size.cols;

        // 獲取移動方向
        let directionIndex = getEnemyDirection(rowCurrent, colCurrent, rowNext, colNext);
        let directionName = getEnemyDirectionName(directionIndex);

        // 驗證方向和MoveRes
        if (directionIndex < 0 || !directionName || !enemy.MoveRes[directionName]) {
            console.error(`無效的敵人移動方向: ${directionIndex}, ${directionName}`);
            console.error("敵人MoveRes:", enemy.MoveRes);
            return;
        }

        // 記錄最後移動的方向
        enemy.lastdirect = directionName;

        // 開始平滑移動動畫
        smoothMoveEnemyToNextCell(
            currentPos,
            nextPos,
            enemy,
            enemyIndex,
            directionName,
            moveSpeed,
            () => {
                // 移動完成後的回調
                currentPathIndex++;
                animateStep(); // 繼續下一步
            }
        );
    }

    // 開始動畫
    animateStep();
}

// 計算兩個位置之間的距離
function calculateDistance(pos1, pos2) {
    let x1 = Math.floor(pos1 / controlLayer[currentLevel].size.cols);
    let y1 = pos1 % controlLayer[currentLevel].size.cols;
    let x2 = Math.floor(pos2 / controlLayer[currentLevel].size.cols);
    let y2 = pos2 % controlLayer[currentLevel].size.cols;
    return Math.abs(x1 - x2) + Math.abs(y1 - y2);
}

// 找出移動範圍中最接近玩家的位置
function findClosestPositionToPlayer(moveRange, attackRange, playeratkrange, players) {
    /*
    優先級：
    1.優先找出可以攻擊到玩家且玩家無法攻擊到敵人的位置
    2.找出可以攻擊到玩家的位置
    3.找出距離玩家最近的位置
    */
    let targetPosition = -1;
    let minDistance = Number.MAX_SAFE_INTEGER;
    let canAttackPlayer = [];
    let canAttackPlayerButPlayerCantAttack = [];
    let cantAttackPlayer = [];
    moveRange.forEach(pos => {
        players.forEach(player => {
            let distance = calculateDistance(pos, player.Position);
            if (distance <= attackRange) {
                if (distance > playeratkrange) {
                    canAttackPlayerButPlayerCantAttack.push(pos);
                } else {
                    canAttackPlayer.push(pos);
                }
            } else {
                cantAttackPlayer.push(pos);
            }
        });
    });
    if (canAttackPlayerButPlayerCantAttack.length > 0) {
        targetPosition = canAttackPlayerButPlayerCantAttack[0];
    } else if (canAttackPlayer.length > 0) {
        targetPosition = canAttackPlayer[0];
    } else if (cantAttackPlayer.length > 0) {
        cantAttackPlayer.forEach(pos => {
            players.forEach(player => {
                let distance = calculateDistance(pos, player.Position);
                if (distance < minDistance) {
                    minDistance = distance;
                    targetPosition = pos;
                }
            });
        });
    }
    return targetPosition;
}

//找出移動範圍中最接近玩家的位置(單一玩家)
function findClosestPositionToPlayerSingle(moveRange, attackRange, playeratkrange,) {
    let targetPosition = -1;
    let minDistance = Number.MAX_SAFE_INTEGER;
    let canAttackPlayer = [];
    let canAttackPlayerButPlayerCantAttack = [];
    let cantAttackPlayer = [];
    moveRange.forEach(pos => {
        let distance = calculateDistance(pos, playeratkrange);
        if (distance <= attackRange) {
            if (distance > playeratkrange) {
                canAttackPlayerButPlayerCantAttack.push(pos);
            } else {
                canAttackPlayer.push(pos);
            }
        } else {
            cantAttackPlayer.push(pos);
        }
    });
    if (canAttackPlayerButPlayerCantAttack.length > 0) {
        targetPosition = canAttackPlayerButPlayerCantAttack[0];
    } else if (canAttackPlayer.length > 0) {
        targetPosition = canAttackPlayer[0];
    } else if (cantAttackPlayer.length > 0) {
        cantAttackPlayer.forEach(pos => {
            let distance = calculateDistance(pos, playeratkrange);
            if (distance < minDistance) {
                minDistance = distance;
                targetPosition = pos;
            }
        });
    }
    return targetPosition;
}

// 取得敵人攻擊範圍
function GetEnemyAykRange(enemyposition, enemyindex, enemyatkrange) {
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    const queue = [{ position: enemyposition, distance: 0 }];
    const visited = new Set();
    visited.add(enemyposition);

    while (queue.length > 0) {
        const { position, distance } = queue.shift();

        if (distance < enemyatkrange) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                // Check boundaries
                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    if (
                        !visited.has(newPosition) && !gameenemys.some(enemy => enemy.Position === newPosition)
                    ) {
                        visited.add(newPosition);
                        queue.push({ position: newPosition, distance: distance + 1 });
                    }
                }
            });
        }
    }

    return visited;
}

//取得敵人潛在攻擊範圍(移動範圍加攻擊範圍)
function GetEnemyPotentialAykRange(enemyposition, enemyindex, enemyatkrange, enemymoverange) {
    let potenticalatkrange = enemyatkrange + enemymoverange;

    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    const queue = [{ position: enemyposition, distance: 0 }];
    const visited = new Set();
    visited.add(enemyposition);

    while (queue.length > 0) {
        const { position, distance } = queue.shift();

        if (distance < potenticalatkrange) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                // Check boundaries
                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    if (
                        !visited.has(newPosition) && !gameenemys.some(enemy => enemy.Position === newPosition)
                    ) {
                        visited.add(newPosition);
                        queue.push({ position: newPosition, distance: distance + 1 });
                    }
                }
            });
        }
    }

    return visited;
}

// 判斷潛在攻擊範圍中是否有玩家
function Booleanhasplayers(potenticalatkrange, players) {
    let hasplayers = false;
    players.forEach(player => {
        if (potenticalatkrange.has(player.Position)) {
            hasplayers = true;
        }
    });
    return hasplayers;
}

function EnemysAction() {
    let enemys = gameenemys;
    clearAllEnemyHighlights();
    clearAllHighlights();
    Dom.GameBoard.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
    Dom.GameBoard.style.backgroundBlendMode = "multiply";
    let players = gameplayers;
    if (enemys.length === 0 || runOBJ["當前行動方"] === "Players" || players.length === 0) {
        return;
    }

    let actionIndex = 0;
    let isActionInProgress = false; // 加入是否有行動的判斷
    setTimeout(() => {
        TurnTextHandle();

        // 在敵人回合開始時處理增援訊息
        setTimeout(() => {
            if (typeof operates !== 'undefined' && operates.processPendingReinforcementMessages) {
                operates.processPendingReinforcementMessages();
            }
        }, 1000);

        setTimeout(() => {
            processEnemyAction();
        }, 4000);
    });

    async function processEnemyAction() {
        let hasbattle = false

        let Diedplayers = await gameplayers.filter(player => player.CurHP <= 0 && player["是否電腦操作"] === false)

        if (Diedplayers.length !== 0) {
            return;
        }

        // 檢查是否還有活著的敵人需要行動
        const aliveEnemies = gameenemys.filter(enemy => enemy.CurHP > 0);
        console.log(`當前actionIndex: ${actionIndex}, 總敵人數: ${gameenemys.length}, 活著的敵人數: ${aliveEnemies.length}`);

        // 跳過已死亡的敵人，找到下一個活著的敵人
        while (actionIndex < gameenemys.length && gameenemys[actionIndex].CurHP <= 0) {
            console.log(`敵人 ${actionIndex} 已死亡，跳過`);
            actionIndex++;
        }

        // 檢查是否所有敵人都已行動完畢或死亡
        if (actionIndex >= gameenemys.length) {
            console.log("所有敵人行動完畢，結束敵人回合");
            gameplayers.forEach(player => {
                player.AlreadyMove = false;
            });
            operates.nextRound();
            return;
        }

        let enemy = gameenemys[actionIndex];
        console.log(`敵人 ${actionIndex} (${enemy.name}) 準備行動，血量: ${enemy.CurHP}/${enemy.HP}`);

        // 雙重檢查敵人是否還活著
        if (!enemy || enemy.CurHP <= 0) {
            console.log(`敵人 ${actionIndex} 已死亡，跳過並繼續下一個`);
            actionIndex++;
            processEnemyAction();
            return;
        }

        if (enemy.CurHP > 0) {
            operates.MoveComera(enemy.Position);

            console.log(`敵人 ${actionIndex} (${enemy.name}) 開始行動`);

            // 優先檢查是否可以使用法術
            console.log(`敵人 ${actionIndex} (${enemy.name}) 開始檢查法術行動選項...`);
            const magicAction = await checkEnemyMagicAction(enemy, actionIndex, players);

            if (magicAction.canUseMagic) {
                console.log(`敵人 ${actionIndex} 選擇使用法術: ${magicAction.magicName}`);

                // 執行法術行動
                await executeEnemyMagicAction(enemy, actionIndex, magicAction);

                // 法術使用完畢，結束該敵人回合
                await wait(2);
                actionIndex++;
                isActionInProgress = false;
                console.log(`敵人 ${actionIndex - 1} 法術行動完成`);

                // 檢查是否所有敵人都已行動完畢
                checkAllEnemiesActionComplete();
                return;
            } else {
                console.log(`敵人 ${actionIndex} (${enemy.name}) 沒有可用的法術，繼續檢查物理攻擊選項...`);
            }

            // 沒有可用法術，使用原本的移動攻擊機制
            let bestPosition = findBestEnemyPosition(actionIndex, enemy.Move, enemy.ATKRange);

            // 檢查智能決策結果
            if (bestPosition === enemy.Position) {
                // 策略決定不移動，直接在當前位置攻擊
                console.log(`敵人 ${actionIndex} 智能決策：不移動，在當前位置攻擊`);

                // 找到當前位置可攻擊的玩家
                let currentAtkRange = GetEnemyAykRange(enemy.Position, actionIndex, enemy.ATKRange);
                let targetPlayer = null;

                // 優先尋找最近的可攻擊玩家
                let minDistance = Infinity;
                for (let player of players) {
                    if (player.CurHP > 0 && currentAtkRange.has(player.Position)) {
                        let distance = calculateManhattanDistance(enemy.Position, player.Position);
                        if (distance < minDistance) {
                            minDistance = distance;
                            targetPlayer = player;
                        }
                    }
                }

                if (targetPlayer) {
                    console.log(`敵人 ${actionIndex} 在當前位置攻擊玩家 ${targetPlayer.name}`);

                    await wait(1);
                    let { battleisended } = await EnemyAccessBattle(enemy, targetPlayer, actionIndex);

                    // 無論戰鬥是否結束，都要繼續下一個敵人
                    await wait(2);
                    actionIndex++;
                    isActionInProgress = false;
                    console.log(`敵人 ${actionIndex - 1} 不移動攻擊完成，battleisended: ${battleisended}，繼續下一個敵人`);
                    processEnemyAction();
                    return;
                } else {
                    console.log(`敵人 ${actionIndex} 當前位置無法攻擊任何玩家，跳過回合`);
                    await wait(1);
                    actionIndex++;
                    isActionInProgress = false;
                    processEnemyAction();
                    return;
                }
            } else if (bestPosition !== enemy.Position) {
                // 需要移動到最佳位置
                console.log(`敵人 ${actionIndex} 移動到最佳位置 ${bestPosition}`);

                if (!isActionInProgress) {
                    isActionInProgress = true;
                    hasbattle = true;

                    await wait(1);
                    // 計算移動路徑並執行移動動畫
                    let movePath = enemyfindpath(enemy.Position, bestPosition, enemy.Move, actionIndex);
                    animateEnemyMove(movePath, actionIndex);

                    await wait(1);
                    operates.MoveComera(bestPosition);
                    await wait(1);

                    // 移動後檢查是否可以攻擊
                    let newAtkRange = GetEnemyAykRange(bestPosition, actionIndex, enemy.ATKRange);
                    let attackTarget = null;

                    // 找到可攻擊的玩家
                    for (let player of players) {
                        if (player.CurHP > 0 && newAtkRange.has(player.Position)) {
                            attackTarget = player;
                            break;
                        }
                    }

                    if (attackTarget) {
                        console.log(`敵人 ${actionIndex} 移動後攻擊玩家 ${attackTarget.name}`);
                        let { battleisended } = await EnemyAccessBattle(enemy, attackTarget, actionIndex);

                        // 無論戰鬥是否結束，都要繼續下一個敵人
                        await wait(2);
                        actionIndex++;
                        isActionInProgress = false;
                        console.log(`敵人 ${actionIndex - 1} 移動後戰鬥完成，battleisended: ${battleisended}，繼續下一個敵人`);
                        processEnemyAction();
                        return;
                    } else {
                        console.log(`敵人 ${actionIndex} 移動完成，但無法攻擊任何玩家`);
                        await wait(1);
                        actionIndex++;
                        isActionInProgress = false;
                        processEnemyAction();
                        return;
                    }
                }
            } else {
                // 無法移動或攻擊，跳過回合
                console.log(`敵人 ${actionIndex} 無法行動，跳過回合`);
                await wait(1);
                actionIndex++;
                isActionInProgress = false;
                processEnemyAction();
                return;
            }
        }
    }

    // 檢查所有敵人是否都已行動完畢（內部函數，可以訪問actionIndex）
    function checkAllEnemiesActionComplete() {
        console.log(`檢查敵人行動完成狀態: actionIndex=${actionIndex}, 總敵人數=${gameenemys.length}`);

        // 跳過已死亡的敵人，找到下一個活著的敵人
        while (actionIndex < gameenemys.length && gameenemys[actionIndex].CurHP <= 0) {
            console.log(`敵人 ${actionIndex} 已死亡，跳過`);
            actionIndex++;
        }

        // 檢查是否所有敵人都已行動完畢或死亡
        if (actionIndex >= gameenemys.length) {
            console.log("所有敵人行動完畢，結束敵人回合");
            gameplayers.forEach(player => {
                player.AlreadyMove = false;
            });
            operates.nextRound();
            return;
        }

        // 還有敵人需要行動，繼續處理
        console.log(`繼續處理敵人 ${actionIndex} 的行動`);
        processEnemyAction();
    }
}

async function EnemyAccessBattle(enemy, player, enemyindex) {
    console.log(`敵人 ${enemy.name} 與玩家 ${player.name} 進入戰鬥`);

    // 戰鬥前讓敵人和玩家面向對方
    await setBattleFacingDirection(enemy, player, enemyindex);

    await wait(1);
    // 創建戰鬥場景、資訊(玩家、敵人血條)
    let { playerCanvas, enemyCanvas } = await CreateBattleInfo(player, enemy);

    //敵人攻擊玩家的命中率
    let playerAvoidRate = (enemy.HitRate - player.AvoidRate) + Math.floor(Math.random() * 100);

    //玩家攻擊敵人的命中率
    let enemyAvoidRate = (player.HitRate - enemy.AvoidRate) + Math.floor(Math.random() * 100);

    //配對閃避的戰鬥情況用
    const getbattlesituation = (playerAvoidRate, enemyAvoidRate) => {
        const situation = {
            "player": "未閃避",
            "enemy": "未閃避"
        };
        if (playerAvoidRate < 100) {
            situation["player"] = "閃避"; // 玩家閃避掉了
        }
        if (enemyAvoidRate < 100) {
            situation["enemy"] = "閃避"; // 敵人閃避掉了
        }
        return situation;
    }

    const matchbattleprocess = async (situation) => {
        let battleisended = false;
        if (situation["player"] === "閃避" && situation["enemy"] === "閃避") {
            console.log("雙方都閃避掉了");
            let { playerisdied } = await enemyAttackfirstMiss(player, enemy, playerCanvas, enemyCanvas);
            if (playerisdied) {
                await gameover(player, playerCanvas)
                return { battleisended: true };
            } else {
                if (isEnemyInRange(player, enemy)) {
                    await wait(0.5);

                    let { isDead } = await playerAttacksecondMiss(player, enemy, playerCanvas, enemyCanvas);

                    if (isDead) {
                        /* 此條件永不觸發，因為閃避傷害為 0 ，所以敵人不會死亡 */
                        let { msgcompleted } = await clearBattleCisforenemy(enemyCanvas, player, enemy, enemyindex)
                        if (msgcompleted) {
                            return { battleisended: true };
                        }
                    } else {
                        let { msgcompleted } = await enemyreturnToMap(player, enemy);
                        if (msgcompleted) {
                            return { battleisended: true };
                        }
                    }
                }
                else {
                    let { msgcompleted } = await enemyreturnToMap(player, enemy);
                    if (msgcompleted) {
                        return { battleisended: true };
                    }
                }
            }
        } else if (situation["player"] === "閃避") {
            console.log("玩家閃避掉了");
            let { playerisdied } = await enemyAttackfirstMiss(player, enemy, playerCanvas, enemyCanvas);

            if (playerisdied) {
                await gameover(player, playerCanvas)
                return { battleisended: true };
            } else {
                if (isEnemyInRange(player, enemy)) {
                    //蓄力無法反擊，但增加下回合移動力
                    if (player["是否蓄力"]) {
                        let { msgcompleted } = await enemyreturnToMap(player, enemy);
                        if (msgcompleted) {
                            return { battleisended: true };
                        }
                    } else {
                        let { isDead } = await playerAttacksecond(player, enemy, playerCanvas, enemyCanvas);

                        if (isDead) {
                            let { msgcompleted } = await clearBattleCisforenemy(enemyCanvas, player, enemy, enemyindex)
                            if (msgcompleted) {
                                return { battleisended: true };
                            }
                        } else {
                            let { msgcompleted } = await enemybattlereturnToMap(player, enemy)
                            if (msgcompleted) {
                                return { battleisended: true };
                            }
                        }
                    }
                } else {
                    let { msgcompleted } = await enemyreturnToMap(player, enemy);
                    if (msgcompleted) {
                        return { battleisended: true };
                    }
                }

            }
        } else if (situation["enemy"] === "閃避") {
            console.log("敵人閃避掉了");
            let { playerisdied } = await enemyAttackfirst(player, enemy, playerCanvas, enemyCanvas);
            if (playerisdied) {
                await gameover(player, playerCanvas)
                return { battleisended: true };
            } else {
                if (isEnemyInRange(player, enemy)) {
                    await wait(0.5);
                    if (player["是否蓄力"]) {
                        let { msgcompleted } = await enemyreturnToMap(player, enemy);
                        if (msgcompleted) {
                            return { battleisended: true };
                        }
                    } else {
                        let { isDead } = await playerAttacksecondMiss(player, enemy, playerCanvas, enemyCanvas);

                        if (isDead) {
                            /* 此條件永不觸發，因為閃避傷害為 0 ，所以敵人不會死亡 */
                            let { msgcompleted } = await clearBattleCisforenemy(enemyCanvas, player, enemy, enemyindex)
                            if (msgcompleted) {
                                return { battleisended: true };
                            }
                        } else {
                            let { msgcompleted } = await enemyreturnToMap(player, enemy);
                            if (msgcompleted) {
                                return { battleisended: true };
                            }
                        }
                    }
                }
                else {
                    let { msgcompleted } = await enemyreturnToMap(player, enemy);
                    if (msgcompleted) {
                        return { battleisended: true };
                    }
                }
            }

        } else {
            console.log("雙方都沒有閃避掉");
            let { playerisdied } = await enemyAttackfirst(player, enemy, playerCanvas, enemyCanvas);
            if (playerisdied) {
                await gameover(player, playerCanvas)
                return { battleisended: true };
            } else {
                if (isEnemyInRange(player, enemy)) {
                    if (player["是否蓄力"]) {
                        let { msgcompleted } = await enemyreturnToMap(player, enemy);
                        if (msgcompleted) {
                            return { battleisended: true };
                        }
                    } else {
                        let { isDead } = await playerAttacksecond(player, enemy, playerCanvas, enemyCanvas);

                        if (isDead) {
                            let { msgcompleted } = await clearBattleCisforenemy(enemyCanvas, player, enemy, enemyindex)
                            if (msgcompleted) {
                                return { battleisended: true };
                            }
                        } else {
                            let { msgcompleted } = await enemybattlereturnToMap(player, enemy)
                            if (msgcompleted) {
                                return { battleisended: true };
                            }
                        }
                    }

                } else {
                    let { msgcompleted } = await enemyreturnToMap(player, enemy);
                    if (msgcompleted) {
                        return { battleisended: true };
                    }
                }
            }
        }

    }

    await wait(0.5);
    let situation = getbattlesituation(playerAvoidRate, enemyAvoidRate);
    console.log(situation);

    let { battleisended } = await matchbattleprocess(situation);
    console.log("戰鬥結束狀態:", battleisended);

    return { battleisended };
}

async function clearBattleCisforenemy(enemyCanvas, player, enemy, enemyindex) {
    await wait(2)
    clearEnemyAnimation(enemyCanvas)
    await wait(1);
    Dom.GameBoard.style.pointerEvents = "auto";
    Dom.GameBoard.style.display = "grid";
    Dom.BattleScreen.style.display = "none";

    // 確保遊戲canvas正確顯示
    const gameCanvas = document.getElementById('gameCanvas');
    if (gameCanvas) {
        gameCanvas.style.display = 'block';
        gameCanvas.style.visibility = 'visible';
    }

    setTimeout(() => {
        Dom.BattleScreen.innerHTML = "";
    })

   

    // 清除敵人的DOM元素（如果存在）
    const enemycell = document.getElementById(enemy.Position);
    if (enemycell) {
        enemycell.classList.remove("enemy");
        enemycell.classList.remove("enemy" + enemyindex);
        enemycell.style.backgroundImage = "";
    }

    // 重新更新mapObjects以移除死亡的敵人
    console.log("更新mapObjects以移除死亡敵人");
    updateMapObjects();

    // 清除所有攻擊相關的事件監聽器和高亮
    if (typeof clearAllAttackRelated === 'function') {
        clearAllAttackRelated();
    }

    // 強制清除任何殘留的點擊事件處理器
    if (window.currentAttackClickHandler) {
        canvas.removeEventListener('click', window.currentAttackClickHandler);
        window.currentAttackClickHandler = null;
    }

    // 重新渲染canvas
    render();

    operates.MoveComera(enemy.Position);

    resetRunOBJ();

    console.log("敵人清除完成，canvas已重新渲染");

    if (player["是否電腦操作"] === false) {
        player.Inventory.push(...enemy.RewardItems);

        player.Inventory = player.Inventory.flat();

        let exp = Math.floor(Math.random() * 50) + 200;
        player.CurEXP += exp;
        let { msgcompleted } = await sysMsg(player, exp, ...enemy.RewardItems);

        // 檢查是否所有敵人都已死亡（CurHP <= 0）
        const aliveEnemies = gameenemys.filter(enemy => enemy.CurHP > 0);
        console.log(`剩餘存活敵人數量: ${aliveEnemies.length}`);

        if (aliveEnemies.length === 0) {
            console.log("所有敵人已死亡，關卡通關！使用與 Players.js 相同的切換方式");

            // 使用與 Players.js 相同的勝利處理方式
            (async () => {
                try {
                    // 檢查新的過渡函數是否存在（現在是全局函數）
                    if (typeof showVictoryMessage !== 'undefined' && typeof transitionToCampScene !== 'undefined') {
                        console.log("使用與 Players.js 相同的過渡系統");

                        // 顯示勝利訊息
                        await showVictoryMessage(currentLevel);

                        // 開始過渡到營地場景
                        await transitionToCampScene(currentLevel);
                    } else {
                        console.warn("全局過渡函數不可用，使用備用方案");
                        // 備用方案：觸發關卡通關事件
                        const levelCompleteEvent = new CustomEvent('levelComplete', {
                            detail: {
                                levelIndex: currentLevel,
                                completionTime: Date.now(),
                                victoryMessage: controlLayer[currentLevel]["勝利訊息"],
                                achievementMessage: controlLayer[currentLevel]["成就訊息"]
                            }
                        });
                        document.dispatchEvent(levelCompleteEvent);
                    }
                } catch (error) {
                    console.error("勝利處理失敗:", error);
                    // 最終備用方案
                    if (typeof sceneManager !== 'undefined' && typeof sceneManager.switchToCamp === 'function') {
                        sceneManager.switchToCamp(currentLevel);
                    }
                }
            })();

            return { msgcompleted: true };
        }

        return { msgcompleted }
    }

    // 對於AI玩家，也要檢查是否所有敵人都已死亡
    const aliveEnemies = gameenemys.filter(enemy => enemy.CurHP > 0);
    console.log(`剩餘存活敵人數量: ${aliveEnemies.length}`);

    if (aliveEnemies.length === 0) {
        console.log("所有敵人已死亡，關卡通關！使用與 Players.js 相同的切換方式");

        // 使用與 Players.js 相同的勝利處理方式
        (async () => {
            try {
                // 檢查新的過渡函數是否存在（現在是全局函數）
                if (typeof showVictoryMessage !== 'undefined' && typeof transitionToCampScene !== 'undefined') {
                    console.log("使用與 Players.js 相同的過渡系統");

                    // 顯示勝利訊息
                    await showVictoryMessage(currentLevel);

                    // 開始過渡到營地場景
                    await transitionToCampScene(currentLevel);
                } else {
                    console.warn("全局過渡函數不可用，使用備用方案");
                    // 備用方案：觸發關卡通關事件
                    const levelCompleteEvent = new CustomEvent('levelComplete', {
                        detail: {
                            levelIndex: currentLevel,
                            completionTime: Date.now(),
                            victoryMessage: controlLayer[currentLevel]["勝利訊息"],
                            achievementMessage: controlLayer[currentLevel]["成就訊息"]
                        }
                    });
                    document.dispatchEvent(levelCompleteEvent);
                }
            } catch (error) {
                console.error("勝利處理失敗:", error);
                // 最終備用方案
                if (typeof sceneManager !== 'undefined' && typeof sceneManager.switchToCamp === 'function') {
                    sceneManager.switchToCamp(currentLevel);
                }
            }
        })();

        return { msgcompleted: true };
    }

    await wait(0.1)
    return { msgcompleted: true }
}

//敵人回到地圖
async function enemyreturnToMap(player, enemy, playerimg) {
    let msgcompleted = false
    await wait(4);
    Dom.GameBoard.style.pointerEvents = "auto";
    Dom.GameBoard.style.display = "grid";
    Dom.BattleScreen.style.animation = "none";
    Dom.BattleScreen.style.display = "none";
    Dom.BattleScreen.innerHTML = "";

    // 確保遊戲canvas正確顯示
    const gameCanvas = document.getElementById('gameCanvas');
    if (gameCanvas) {
        gameCanvas.style.display = 'block';
        gameCanvas.style.visibility = 'visible';
    }

    operates.MoveComera(enemy.Position);
    msgcompleted = true
    resetRunOBJ();
    return { msgcompleted }
}

//敵人由戰鬥回到地圖
async function enemybattlereturnToMap(player, enemy) {
    await wait(4);
    Dom.GameBoard.style.pointerEvents = "auto";
    Dom.GameBoard.style.display = "grid";
    Dom.BattleScreen.style.animation = "none";
    Dom.BattleScreen.style.display = "none";
    Dom.BattleScreen.innerHTML = "";

    // 確保遊戲canvas正確顯示
    const gameCanvas = document.getElementById('gameCanvas');
    if (gameCanvas) {
        gameCanvas.style.display = 'block';
        gameCanvas.style.visibility = 'visible';
    }

    operates.MoveComera(enemy.Position);

    resetRunOBJ();

    if (player["是否電腦操作"] === false) {
        let exp = Math.floor(Math.random() * 50) + 200;
        player.CurEXP += exp;
        let { msgcompleted } = await sysMsg(player, exp, null);
        return { msgcompleted }
    }
    await wait(0.1)
    return { msgcompleted: true }
}

// 超智能敵人尋找機制：考慮不移動攻擊最近vs移動攻擊範圍內最弱的策略選擇
function findBestEnemyPosition(enemyIndex, moveRange, attackRange) {
    console.log(`敵人 ${enemyIndex} 開始超智能尋找最佳位置`);

    const enemy = gameenemys[enemyIndex];
    if (!enemy || enemy.CurHP <= 0) {
        console.warn(`敵人 ${enemyIndex} 不存在或已死亡`);
        return enemy.Position;
    }

    // 獲取所有活著的玩家並計算戰力和距離
    const alivePlayers = gameplayers.filter(player => player.CurHP > 0);
    if (alivePlayers.length === 0) {
        console.log("沒有活著的玩家，敵人保持原位");
        return enemy.Position;
    }

    // 計算每個玩家的戰力和距離
    const playersWithInfo = alivePlayers.map(player => ({
        player: player,
        position: player.Position,
        power: calculatePlayerPower(player),
        distance: calculateManhattanDistance(enemy.Position, player.Position)
    }));

    // 找到最近的玩家
    const nearestPlayer = playersWithInfo.reduce((nearest, current) =>
        current.distance < nearest.distance ? current : nearest
    );

    // 獲取敵人可移動的位置
    const moveablePositions = bfsforenemy(enemy.Position, moveRange, enemyIndex);

    // 找到移動範圍內可攻擊的玩家中最弱的（相對最弱，而非絕對最弱）
    let weakestPlayerInRange = null;
    let minPowerInRange = Infinity;

    for (let position of moveablePositions) {
        for (let playerInfo of playersWithInfo) {
            const distanceFromPosition = calculateManhattanDistance(position, playerInfo.position);
            if (distanceFromPosition <= attackRange) {
                // 這個玩家在這個移動位置的攻擊範圍內
                if (playerInfo.power < minPowerInRange) {
                    minPowerInRange = playerInfo.power;
                    weakestPlayerInRange = playerInfo;
                }
            }
        }
    }

    console.log(`移動範圍內最弱玩家: ${weakestPlayerInRange?.player.name || '無'} (戰力: ${minPowerInRange === Infinity ? '無' : minPowerInRange})`);
    console.log(`最近玩家: ${nearestPlayer.player.name} (距離: ${nearestPlayer.distance})`);

    // 檢查當前位置是否能攻擊到最近的玩家
    const canAttackNearestWithoutMove = nearestPlayer.distance <= attackRange;

    // 檢查移動後是否能攻擊到移動範圍內的最弱玩家
    const canAttackWeakestWithMove = weakestPlayerInRange !== null;

    console.log(`不移動攻擊最近玩家: ${canAttackNearestWithoutMove}`);
    console.log(`移動後攻擊範圍內最弱玩家: ${canAttackWeakestWithMove}`);

    let targetPlayer;
    let shouldMove = false;

    if (canAttackNearestWithoutMove && canAttackWeakestWithMove) {
        // 情況1: 既能不移動攻擊最近，也能移動攻擊範圍內最弱
        // 優先選擇移動攻擊範圍內最弱玩家（更有戰略價值）
        targetPlayer = weakestPlayerInRange;
        shouldMove = true;
        console.log(`敵人 ${enemyIndex} 選擇策略: 移動攻擊範圍內最弱玩家 ${weakestPlayerInRange.player.name} (戰略優先)`);
    } else if (canAttackNearestWithoutMove) {
        // 情況2: 只能不移動攻擊最近玩家
        targetPlayer = nearestPlayer;
        shouldMove = false;
        console.log(`敵人 ${enemyIndex} 選擇策略: 不移動攻擊最近玩家 ${nearestPlayer.player.name}`);
    } else if (canAttackWeakestWithMove) {
        // 情況3: 只能移動攻擊範圍內最弱玩家
        targetPlayer = weakestPlayerInRange;
        shouldMove = true;
        console.log(`敵人 ${enemyIndex} 選擇策略: 移動攻擊範圍內最弱玩家 ${weakestPlayerInRange.player.name}`);
    } else {
        // 情況4: 都無法攻擊，選擇最近玩家接近
        targetPlayer = nearestPlayer;
        shouldMove = true;
        console.log(`敵人 ${enemyIndex} 選擇策略: 接近最近玩家 ${nearestPlayer.player.name}`);
    }

    // 根據策略決定最終位置
    let bestPosition;
    if (!shouldMove && canAttackNearestWithoutMove) {
        // 不移動，保持當前位置
        bestPosition = enemy.Position;
        console.log(`敵人 ${enemyIndex} 保持當前位置 ${bestPosition} 攻擊最近玩家`);
    } else {
        // 需要移動，使用A*算法找到最佳位置
        bestPosition = findBestPositionWithAStar(enemy, enemyIndex, targetPlayer.position, moveRange, attackRange);
        console.log(`敵人 ${enemyIndex} 移動到位置 ${bestPosition} 執行策略`);
    }

    console.log(`敵人 ${enemyIndex} 最終決策: 位置${bestPosition}，目標${targetPlayer.player.name}，移動${shouldMove}`);
    return bestPosition;
}

// 檢查是否能在當前回合攻擊到指定玩家
function canAttackPlayerThisTurn(enemy, enemyIndex, targetPlayerInfo, moveRange, attackRange) {
    console.log(`檢查敵人 ${enemyIndex} 是否能攻擊玩家 ${targetPlayerInfo.player.name}`);

    // 獲取敵人可移動的位置
    const moveablePositions = bfsforenemy(enemy.Position, moveRange, enemyIndex);

    // 檢查每個可移動位置是否能攻擊到目標玩家
    for (let position of moveablePositions) {
        const distance = calculateManhattanDistance(position, targetPlayerInfo.position);

        if (distance <= attackRange) {
            console.log(`敵人 ${enemyIndex} 可以從位置 ${position} 攻擊玩家 ${targetPlayerInfo.player.name} (距離: ${distance})`);
            return {
                canAttack: true,
                attackPosition: position,
                distance: distance
            };
        }
    }

    console.log(`敵人 ${enemyIndex} 無法在當前回合攻擊玩家 ${targetPlayerInfo.player.name}`);
    return {
        canAttack: false,
        attackPosition: null,
        distance: Infinity
    };
}

// 計算玩家戰力 (防禦 + 生命)
function calculatePlayerPower(player) {
    return player.CurHP + player.DEF;
}

// 計算曼哈頓距離
function calculateManhattanDistance(pos1, pos2) {
    const col1 = pos1 % controlLayer[currentLevel].size.cols;
    const row1 = Math.floor(pos1 / controlLayer[currentLevel].size.cols);
    const col2 = pos2 % controlLayer[currentLevel].size.cols;
    const row2 = Math.floor(pos2 / controlLayer[currentLevel].size.cols);

    return Math.abs(col1 - col2) + Math.abs(row1 - row2);
}

// 使用A*算法找到最佳移動位置
function findBestPositionWithAStar(enemy, enemyIndex, targetPosition, moveRange, attackRange) {
    console.log(`使用A*算法為敵人 ${enemyIndex} 尋找到達目標 ${targetPosition} 的最佳位置`);

    // 獲取敵人可移動的位置
    const moveablePositions = bfsforenemy(enemy.Position, moveRange, enemyIndex);
    if (moveablePositions.size === 0) {
        console.log(`敵人 ${enemyIndex} 沒有可移動的位置`);
        return enemy.Position;
    }

    let bestPosition = enemy.Position;
    let bestScore = -Infinity;

    // 評估每個可移動位置
    for (let position of moveablePositions) {
        const score = evaluatePositionWithAStar(position, targetPosition, enemy, enemyIndex, attackRange);

        console.log(`位置 ${position} 的A*評分: ${score}`);

        if (score > bestScore) {
            bestScore = score;
            bestPosition = position;
        }
    }

    return bestPosition;
}

// A*算法的位置評估函數 - 優化版
function evaluatePositionWithAStar(position, targetPosition, enemy, enemyIndex, attackRange) {
    let score = 0;

    // 計算到目標的實際距離 (A*的核心：g(n) + h(n))
    const actualDistance = calculateManhattanDistance(position, targetPosition);

    // A*評分：距離越近分數越高
    score += Math.max(0, 1000 - actualDistance * 30); // 基礎距離分數

    // 檢查是否在攻擊範圍內 - 最高優先級
    if (actualDistance <= attackRange) {
        score += 5000; // 可以攻擊目標，最高優先級
        console.log(`位置 ${position} 可以攻擊目標，額外加分 5000`);

        // 如果能攻擊，距離越近越好
        score += (attackRange - actualDistance) * 100;
    } else {
        // 無法攻擊時，優先選擇能最快接近目標的位置
        const approachScore = Math.max(0, 500 - actualDistance * 20);
        score += approachScore;
    }

    // 避免與其他敵人重疊 - 嚴重懲罰
    for (let i = 0; i < gameenemys.length; i++) {
        if (i !== enemyIndex && gameenemys[i].CurHP > 0) {
            if (gameenemys[i].Position === position) {
                score -= 10000; // 嚴重懲罰重疊位置
                console.log(`位置 ${position} 與敵人 ${i} 重疊，懲罰 -10000`);
            }
        }
    }

    // 路徑成本考量 (A*的g(n)部分)
    const pathCost = calculatePathCost(enemy.Position, position);
    score -= pathCost * 5; // 路徑越長，分數稍微降低

    // 戰術位置評估
    const tacticalScore = evaluateTacticalPosition(position, targetPosition);
    score += tacticalScore;

    // 避免被玩家包圍的位置
    const threatScore = evaluatePositionThreat(position, enemyIndex);
    score -= threatScore;

    console.log(`位置 ${position}: 距離${actualDistance}, 攻擊範圍${actualDistance <= attackRange}, 路徑成本${pathCost}, 戰術分數${tacticalScore}, 威脅分數${threatScore}, 總分${score}`);

    return score;
}

// 評估位置的威脅程度
function evaluatePositionThreat(position, enemyIndex) {
    let threatScore = 0;

    // 檢查周圍玩家的威脅
    for (let player of gameplayers) {
        if (player.CurHP <= 0) continue;

        const distance = calculateManhattanDistance(position, player.Position);
        const playerAttackRange = getPlayerAttackRange(player);

        if (distance <= playerAttackRange) {
            // 在玩家攻擊範圍內，增加威脅分數
            const playerPower = calculatePlayerPower(player);
            threatScore += 30 + (playerPower * 0.3); // 根據玩家戰力調整威脅
        }
    }

    return threatScore;
}

// 計算路徑成本 (A*的g(n)部分)
function calculatePathCost(startPosition, endPosition) {
    // 使用簡化的路徑成本計算
    // 在實際A*實現中，這應該是實際路徑的成本
    const distance = calculateManhattanDistance(startPosition, endPosition);

    // 檢查路徑上是否有障礙物，增加成本
    let obstaclePenalty = 0;
    const startCol = startPosition % controlLayer[currentLevel].size.cols;
    const startRow = Math.floor(startPosition / controlLayer[currentLevel].size.cols);
    const endCol = endPosition % controlLayer[currentLevel].size.cols;
    const endRow = Math.floor(endPosition / controlLayer[currentLevel].size.cols);

    // 簡單的直線路徑檢查
    const deltaCol = endCol - startCol;
    const deltaRow = endRow - startRow;
    const steps = Math.max(Math.abs(deltaCol), Math.abs(deltaRow));

    if (steps > 0) {
        for (let i = 1; i <= steps; i++) {
            const checkCol = startCol + Math.round((deltaCol * i) / steps);
            const checkRow = startRow + Math.round((deltaRow * i) / steps);
            const checkPos = checkRow * controlLayer[currentLevel].size.cols + checkCol;

            if (gameobstacles.includes(checkPos)) {
                obstaclePenalty += 5; // 每個障礙物增加成本
            }
        }
    }

    return distance + obstaclePenalty;
}

// 評估戰術位置
function evaluateTacticalPosition(position, targetPosition) {
    let tacticalScore = 0;

    const posCol = position % controlLayer[currentLevel].size.cols;
    const posRow = Math.floor(position / controlLayer[currentLevel].size.cols);
    const targetCol = targetPosition % controlLayer[currentLevel].size.cols;
    const targetRow = Math.floor(targetPosition / controlLayer[currentLevel].size.cols);

    // 1. 避免邊緣位置（除非目標在邊緣）
    const edgeDistance = Math.min(
        posCol,
        posRow,
        controlLayer[currentLevel].size.cols - 1 - posCol,
        controlLayer[currentLevel].size.rows - 1 - posRow
    );
    tacticalScore += edgeDistance * 2;

    // 2. 考慮與目標的相對位置
    const deltaCol = targetCol - posCol;
    const deltaRow = targetRow - posRow;

    // 偏好直線攻擊路徑
    if (deltaCol === 0 || deltaRow === 0 || Math.abs(deltaCol) === Math.abs(deltaRow)) {
        tacticalScore += 20; // 直線或對角線位置加分
    }

    // 3. 周圍掩護評估
    const coverScore = evaluateCoverPosition(position);
    tacticalScore += coverScore;

    // 4. 避免被多個玩家包圍
    const surroundingThreat = evaluateSurroundingThreat(position);
    tacticalScore -= surroundingThreat;

    return tacticalScore;
}

// 評估掩護位置
function evaluateCoverPosition(position) {
    const col = position % controlLayer[currentLevel].size.cols;
    const row = Math.floor(position / controlLayer[currentLevel].size.cols);

    let coverScore = 0;
    const directions = [
        { x: 0, y: 1 }, { x: 1, y: 0 }, { x: 0, y: -1 }, { x: -1, y: 0 },
        { x: 1, y: 1 }, { x: -1, y: 1 }, { x: 1, y: -1 }, { x: -1, y: -1 }
    ];

    for (let dir of directions) {
        const newCol = col + dir.x;
        const newRow = row + dir.y;

        // 檢查邊界
        if (newCol < 0 || newCol >= controlLayer[currentLevel].size.cols ||
            newRow < 0 || newRow >= controlLayer[currentLevel].size.rows) {
            coverScore += 3; // 邊界提供掩護
            continue;
        }

        const newPos = newRow * controlLayer[currentLevel].size.cols + newCol;
        if (gameobstacles.includes(newPos)) {
            coverScore += 5; // 障礙物提供掩護
        }
    }

    return coverScore;
}

// 評估周圍威脅
function evaluateSurroundingThreat(position) {
    const col = position % controlLayer[currentLevel].size.cols;
    const row = Math.floor(position / controlLayer[currentLevel].size.cols);

    let threatScore = 0;

    // 檢查周圍是否有玩家
    for (let player of gameplayers) {
        if (player.CurHP <= 0) continue;

        const playerDistance = calculateManhattanDistance(position, player.Position);
        const playerAttackRange = getPlayerAttackRange(player);

        if (playerDistance <= playerAttackRange) {
            // 在玩家攻擊範圍內，增加威脅分數
            threatScore += 50;

            // 根據玩家戰力調整威脅程度
            const playerPower = calculatePlayerPower(player);
            threatScore += playerPower * 0.5;
        }
    }

    return threatScore;
}

// 評估敵人位置的分數
function evaluateEnemyPosition(position, enemy, alivePlayers, attackRange, enemyIndex) {
    let score = 0;

    // 計算位置座標
    const posCol = position % controlLayer[currentLevel].size.cols;
    const posRow = Math.floor(position / controlLayer[currentLevel].size.cols);

    // 評估每個玩家
    for (let player of alivePlayers) {
        const playerCol = player.Position % controlLayer[currentLevel].size.cols;
        const playerRow = Math.floor(player.Position / controlLayer[currentLevel].size.cols);

        // 計算曼哈頓距離
        const distance = Math.abs(posCol - playerCol) + Math.abs(posRow - playerRow);

        // 1. 攻擊範圍內的玩家 - 最高優先級
        if (distance <= attackRange) {
            score += 1000; // 可以攻擊玩家，最高分

            // 檢查玩家是否能反擊
            const playerAttackRange = getPlayerAttackRange(player);
            if (distance > playerAttackRange) {
                score += 500; // 玩家無法反擊，額外加分
            }

            // 優先攻擊血量低的玩家
            const healthRatio = player.CurHP / player.HP;
            score += (1 - healthRatio) * 200; // 血量越低，分數越高
        }
        // 2. 接近玩家 - 中等優先級
        else {
            // 距離越近分數越高，但不如攻擊範圍內的分數
            score += Math.max(0, 100 - distance * 10);
        }
    }

    // 3. 避免與其他敵人重疊
    for (let i = 0; i < gameenemys.length; i++) {
        if (i !== enemyIndex && gameenemys[i].CurHP > 0) {
            if (gameenemys[i].Position === position) {
                score -= 10000; // 嚴重懲罰重疊位置
            }
        }
    }

    // 4. 地形考量 - 避免角落和死路
    const edgeDistance = Math.min(
        posCol,
        posRow,
        controlLayer[currentLevel].size.cols - 1 - posCol,
        controlLayer[currentLevel].size.rows - 1 - posRow
    );
    score += edgeDistance * 5; // 稍微偏好中央位置

    // 5. 戰術位置 - 考慮周圍的障礙物和掩護
    const surroundingObstacles = countSurroundingObstacles(position);
    score += surroundingObstacles * 10; // 有掩護的位置稍微加分

    return score;
}

// 獲取玩家的攻擊範圍
function getPlayerAttackRange(player) {
    // 根據玩家的武器或技能計算攻擊範圍
    // 這裡使用簡單的預設值，可以根據實際遊戲邏輯調整
    if (player.weapon && player.weapon.range) {
        return player.weapon.range;
    }
    return 1; // 預設近戰攻擊範圍
}

// 計算周圍障礙物數量
function countSurroundingObstacles(position) {
    const col = position % controlLayer[currentLevel].size.cols;
    const row = Math.floor(position / controlLayer[currentLevel].size.cols);

    let obstacleCount = 0;
    const directions = [
        { x: 0, y: 1 }, { x: 1, y: 0 }, { x: 0, y: -1 }, { x: -1, y: 0 },
        { x: 1, y: 1 }, { x: -1, y: 1 }, { x: 1, y: -1 }, { x: -1, y: -1 }
    ];

    for (let dir of directions) {
        const newCol = col + dir.x;
        const newRow = row + dir.y;

        // 檢查邊界
        if (newCol < 0 || newCol >= controlLayer[currentLevel].size.cols ||
            newRow < 0 || newRow >= controlLayer[currentLevel].size.rows) {
            obstacleCount++;
            continue;
        }

        const newPos = newRow * controlLayer[currentLevel].size.cols + newCol;
        if (gameobstacles.includes(newPos)) {
            obstacleCount++;
        }
    }

    return obstacleCount;
}

// 設置戰鬥時的面向方向
async function setBattleFacingDirection(enemy, player, enemyIndex) {
    console.log(`設置戰鬥面向：敵人 ${enemy.name} 與玩家 ${player.name}`);

    // 計算敵人和玩家的位置
    const enemyCol = enemy.Position % controlLayer[currentLevel].size.cols;
    const enemyRow = Math.floor(enemy.Position / controlLayer[currentLevel].size.cols);
    const playerCol = player.Position % controlLayer[currentLevel].size.cols;
    const playerRow = Math.floor(player.Position / controlLayer[currentLevel].size.cols);

    // 計算敵人應該面向玩家的方向
    const enemyDirection = calculateFacingDirection(enemyCol, enemyRow, playerCol, playerRow);
    console.log(`敵人 ${enemy.name} 轉向: ${enemyDirection}`);

    // 計算玩家應該面向敵人的方向
    const playerDirection = calculateFacingDirection(playerCol, playerRow, enemyCol, enemyRow);
    console.log(`玩家 ${player.name} 轉向: ${playerDirection}`);

    // 更新敵人的面向方向
    enemy.lastdirect = enemyDirection;

    // 更新玩家的面向方向
    player.lastdirect = playerDirection;

    // 更新敵人在canvas中的圖片
    await updateEnemyFacingImage(enemy, enemyIndex, enemyDirection);

    // 更新玩家在canvas中的圖片
    await updatePlayerFacingImage(player, playerDirection);

    // 重新渲染
    render();

    console.log(`戰鬥面向設置完成：敵人面向${enemyDirection}，玩家面向${playerDirection}`);
}

// 計算面向方向
function calculateFacingDirection(fromCol, fromRow, toCol, toRow) {
    const deltaCol = toCol - fromCol;
    const deltaRow = toRow - fromRow;

    // 優先考慮主要方向（水平或垂直）
    if (Math.abs(deltaCol) > Math.abs(deltaRow)) {
        // 水平方向為主
        return deltaCol > 0 ? 'right' : 'left';
    } else if (Math.abs(deltaRow) > Math.abs(deltaCol)) {
        // 垂直方向為主
        return deltaRow > 0 ? 'down' : 'up';
    } else {
        // 對角線情況，選擇垂直方向
        return deltaRow > 0 ? 'down' : 'up';
    }
}

// 更新敵人面向圖片
async function updateEnemyFacingImage(enemy, enemyIndex, direction) {
    try {
        console.log(`更新敵人 ${enemy.name} 面向圖片: ${direction}`);

        // 獲取對應方向的站立圖片
        let standImages = enemy.Stand && enemy.Stand[direction];
        if (!standImages || !Array.isArray(standImages) || standImages.length === 0) {
            console.warn(`敵人 ${enemy.name} 沒有 ${direction} 方向的站立圖片，使用down方向`);
            standImages = enemy.Stand && enemy.Stand['down'];
            if (!standImages) {
                console.warn(`敵人 ${enemy.name} 沒有站立圖片`);
                return;
            }
        }

        // 找到敵人物件
        let enemyObj = mapObjects.find(obj =>
            obj.type === 'enemy' && obj.enemyIndex === enemyIndex
        );

        if (enemyObj) {
            // 載入新的站立圖片
            const standImageSrc = standImages[0];
            const standImg = await preloadImage(standImageSrc);

            // 更新敵人物件
            enemyObj.img = standImg;
            enemyObj.standImages = standImages;
            enemyObj.currentFrameIndex = 0;

            console.log(`敵人 ${enemy.name} 圖片已更新為 ${direction} 方向`);
        } else {
            console.warn(`找不到敵人 ${enemyIndex} 的canvas物件`);
        }
    } catch (error) {
        console.error(`更新敵人面向圖片時發生錯誤:`, error);
    }
}

// 更新玩家面向圖片
async function updatePlayerFacingImage(player, direction) {
    try {
        console.log(`更新玩家 ${player.name} 面向圖片: ${direction}`);

        // 獲取玩家索引
        const playerIndex = gameplayers.findIndex(p => p === player);
        if (playerIndex === -1) {
            console.error("找不到玩家索引");
            return;
        }

        // 獲取對應方向的站立圖片
        let standImages = player.Stand && player.Stand[direction];
        if (!standImages || !Array.isArray(standImages) || standImages.length === 0) {
            console.warn(`玩家 ${player.name} 沒有 ${direction} 方向的站立圖片，使用down方向`);
            standImages = player.Stand && player.Stand['down'];
            if (!standImages) {
                console.warn(`玩家 ${player.name} 沒有站立圖片`);
                return;
            }
        }

        // 找到玩家物件
        let playerObj = mapObjects.find(obj =>
            obj.type === 'player' && obj.playerIndex === playerIndex
        );

        if (playerObj) {
            // 載入新的站立圖片
            const standImageSrc = standImages[0];
            const standImg = await preloadImage(standImageSrc);

            // 更新玩家物件
            playerObj.img = standImg;
            playerObj.standImages = standImages;
            playerObj.currentFrameIndex = 0;

            console.log(`玩家 ${player.name} 圖片已更新為 ${direction} 方向`);
        } else {
            console.warn(`找不到玩家 ${playerIndex} 的canvas物件`);
        }
    } catch (error) {
        console.error(`更新玩家面向圖片時發生錯誤:`, error);
    }
}

// 檢查敵人是否可以使用法術
async function checkEnemyMagicAction(enemy, enemyIndex, players) {
    console.log(`檢查敵人 ${enemyIndex} (${enemy.name}) 的法術行動選項`);

    // 檢查敵人是否有法術
    if (!enemy.法術 || enemy.法術.length === 0) {
        console.log(`敵人 ${enemyIndex} 沒有法術`);
        return { canUseMagic: false };
    }

    // 檢查每個法術是否可用
    for (const magic of enemy.法術) {
        const needMP = magic.NeedMP || 0;
        const currentMP = enemy.CurMP || 0;

        // 檢查MP是否足夠
        if (currentMP < needMP) {
            console.log(`敵人 ${enemyIndex} MP不足，無法使用 ${magic.name} (需要${needMP}, 當前${currentMP})`);
            continue;
        }

        const classify = magic.Classify || "";

        if (classify === "治癒") {
            // 治癒法術：檢查是否有友軍需要治療
            const healResult = findEnemyHealTarget(enemy, enemyIndex, magic);
            if (healResult && healResult.target) {
                console.log(`敵人 ${enemyIndex} 可以對 ${healResult.target.name} 使用治癒法術 ${magic.name}, 目標位置: ${healResult.targetPosition}`);
                return {
                    canUseMagic: true,
                    magicName: magic.name,
                    magic: magic,
                    actionType: "heal",
                    target: healResult.target,
                    targetPosition: healResult.targetPosition
                };
            }
        } else if (classify === "火系" || classify === "雷系" || classify === "冰系" || classify === "幻系" || classify === "華系" || classify === "冥系") {
            // 攻擊法術：檢查是否有玩家在施法範圍內
            const attackTarget = findEnemyAttackMagicTarget(enemy, enemyIndex, magic, players);
            if (attackTarget) {
                console.log(`敵人 ${enemyIndex} 可以對 ${attackTarget.target.name} 使用攻擊法術 ${magic.name}`);
                return {
                    canUseMagic: true,
                    magicName: magic.name,
                    magic: magic,
                    actionType: "attack",
                    target: attackTarget.target,
                    targetPosition: attackTarget.targetPosition
                };
            }
        }
    }

    console.log(`敵人 ${enemyIndex} 沒有可用的法術行動`);
    return { canUseMagic: false };
}

// 尋找需要治療的友軍（智能瞄準版本）
function findEnemyHealTarget(caster, casterIndex, magic) {
    const distance = magic.distance || 0;
    const range = magic.Range || 0;

    console.log(`智能尋找治療目標: 施法距離=${distance}, 效果範圍=${range}`);

    let bestTarget = null;
    let bestTargetPosition = null;
    let maxAlliesHealed = 0;

    // 檢查所有活著的敵人（包括自己）
    for (let i = 0; i < gameenemys.length; i++) {
        const ally = gameenemys[i];
        if (ally.CurHP <= 0) continue; // 跳過死亡的友軍

        // 使用即時血量數據檢查是否需要治療
        const currentHP = ally.CurHP;
        const maxHP = ally.HP;
        const healthRatio = currentHP / maxHP;

        if (healthRatio >= 0.5) continue; // 血量充足，不需要治療

        console.log(`檢查敵人 ${i} (${ally.name}) 是否需要治療: 即時血量 ${currentHP}/${maxHP} (${(healthRatio * 100).toFixed(1)}%)`);

        // 檢查是否可以找到一個施法位置來治療這個友軍
        const healResult = findBestMagicHealPosition(caster, ally, distance, range, casterIndex, i);

        if (healResult.canHeal) {
            console.log(`可以治療友軍 ${ally.name}: 施法位置=${healResult.castPosition}, 目標位置=${healResult.targetPosition}, 可治療友軍數=${healResult.alliesHealed}`);

            // 計算治療優先級分數
            let healScore = healResult.alliesHealed * 100; // 基礎分數：能治療的友軍數量

            // 自我治療額外加分
            if (i === casterIndex) {
                healScore += 500; // 自我治療優先級
            }

            // 血量越低，優先級越高
            const urgencyScore = (1 - healthRatio) * 200;
            healScore += urgencyScore;

            console.log(`治療方案評分: ${ally.name} - 總分${healScore} (友軍數${healResult.alliesHealed}*100 + 自我治療${i === casterIndex ? 500 : 0} + 緊急度${urgencyScore.toFixed(1)})`);

            // 選擇能治療最多友軍的方案，或者優先級最高的方案
            if (healResult.alliesHealed > maxAlliesHealed ||
                (healResult.alliesHealed === maxAlliesHealed && healScore > (bestTarget ? calculateHealScore(bestTarget, casterIndex) : 0))) {
                maxAlliesHealed = healResult.alliesHealed;
                bestTarget = ally;
                bestTargetPosition = healResult.targetPosition;
            }
        } else {
            console.log(`無法治療友軍 ${ally.name}: 距離太遠或無法找到合適的施法位置`);
        }
    }

    if (bestTarget) {
        console.log(`選擇最佳治療目標: ${bestTarget.name}, 目標位置: ${bestTargetPosition}, 可治療友軍數: ${maxAlliesHealed}`);
        return {
            target: bestTarget,
            targetPosition: bestTargetPosition
        };
    }

    console.log("沒有找到可治療的友軍");
    return null;
}

// 計算治療分數（輔助函數）
function calculateHealScore(ally, casterIndex) {
    const healthRatio = ally.CurHP / ally.HP;
    let score = (1 - healthRatio) * 200; // 緊急度分數
    if (gameenemys.indexOf(ally) === casterIndex) {
        score += 500; // 自我治療加分
    }
    return score;
}

// 尋找最佳治療位置
function findBestMagicHealPosition(caster, targetAlly, distance, range, casterIndex, targetIndex) {
    const casterPosition = caster.Position;
    const targetPosition = targetAlly.Position;

    // 檢查是否可以直接瞄準友軍位置
    const directDistance = calculateManhattanDistance(casterPosition, targetPosition);
    if (directDistance <= distance) {
        // 可以直接瞄準友軍位置，檢查範圍內能治療多少友軍
        const alliesInRange = getEnemiesInMagicRange(targetPosition, range);
        const needHealAllies = alliesInRange.filter(ally =>
            ally.CurHP > 0 && (ally.CurHP / ally.HP) < 0.5
        );

        console.log(`直接瞄準 ${targetAlly.name}: 距離=${directDistance}, 範圍內需治療友軍數=${needHealAllies.length}`);

        return {
            canHeal: true,
            castPosition: casterPosition,
            targetPosition: targetPosition,
            alliesHealed: needHealAllies.length
        };
    }

    // 無法直接瞄準，嘗試找到一個施法位置使得友軍在治療範圍內
    console.log(`無法直接瞄準 ${targetAlly.name} (距離=${directDistance} > 施法距離=${distance}), 尋找間接治療位置...`);

    let bestResult = { canHeal: false, alliesHealed: 0 };

    // 檢查施法距離內的所有可能目標位置
    for (let row = 0; row < controlLayer[currentLevel].size.rows; row++) {
        for (let col = 0; col < controlLayer[currentLevel].size.cols; col++) {
            const potentialTargetPos = row * controlLayer[currentLevel].size.cols + col;

            // 檢查這個位置是否在施法距離內
            const distanceToTarget = calculateManhattanDistance(casterPosition, potentialTargetPos);
            if (distanceToTarget <= distance) {
                // 檢查目標友軍是否在這個位置的治療範圍內
                const alliesInRange = getEnemiesInMagicRange(potentialTargetPos, range);
                const isTargetInRange = alliesInRange.some(ally => ally.Position === targetPosition && ally.CurHP > 0);

                if (isTargetInRange) {
                    const needHealAllies = alliesInRange.filter(ally =>
                        ally.CurHP > 0 && (ally.CurHP / ally.HP) < 0.5
                    );

                    if (needHealAllies.length > bestResult.alliesHealed) {
                        bestResult = {
                            canHeal: true,
                            castPosition: casterPosition,
                            targetPosition: potentialTargetPos,
                            alliesHealed: needHealAllies.length
                        };

                        console.log(`找到更好的間接治療位置: 目標位置=${potentialTargetPos}, 可治療友軍數=${needHealAllies.length}`);
                    }
                }
            }
        }
    }

    return bestResult;
}

// 尋找攻擊法術的目標（智能瞄準版本）
function findEnemyAttackMagicTarget(caster, casterIndex, magic, players) {
    const distance = magic.distance || 0;
    const range = magic.Range || 0;

    console.log(`智能尋找攻擊法術目標: 施法距離=${distance}, 效果範圍=${range}`);

    let bestTarget = null;
    let bestTargetPosition = null;
    let maxPlayersHit = 0;

    // 檢查所有活著的玩家
    for (const player of players) {
        if (player.CurHP <= 0) continue; // 跳過死亡的玩家

        console.log(`檢查玩家 ${player.name} 作為潛在目標...`);

        // 檢查是否可以找到一個施法位置來攻擊這個玩家
        const attackResult = findBestMagicAttackPosition(caster, player, distance, range);

        if (attackResult.canAttack) {
            console.log(`可以攻擊玩家 ${player.name}: 施法位置=${attackResult.castPosition}, 目標位置=${attackResult.targetPosition}, 可命中玩家數=${attackResult.playersHit}`);

            // 選擇能命中最多玩家的攻擊方案
            if (attackResult.playersHit > maxPlayersHit) {
                maxPlayersHit = attackResult.playersHit;
                bestTarget = player;
                bestTargetPosition = attackResult.targetPosition;
            }
        } else {
            console.log(`無法攻擊玩家 ${player.name}: 距離太遠或無法找到合適的施法位置`);
        }
    }

    if (bestTarget) {
        console.log(`選擇最佳攻擊目標: ${bestTarget.name}, 目標位置: ${bestTargetPosition}, 可命中玩家數: ${maxPlayersHit}`);
        return {
            target: bestTarget,
            targetPosition: bestTargetPosition
        };
    }

    console.log("沒有找到可攻擊的目標");
    return null;
}

// 尋找最佳法術攻擊位置
function findBestMagicAttackPosition(caster, targetPlayer, distance, range) {
    const casterPosition = caster.Position;
    const targetPosition = targetPlayer.Position;

    // 檢查是否可以直接瞄準玩家位置
    const directDistance = calculateManhattanDistance(casterPosition, targetPosition);
    if (directDistance <= distance) {
        // 可以直接瞄準玩家位置，檢查範圍內能命中多少玩家
        const playersInRange = getPlayersInMagicRange(targetPosition, range);
        const alivePlayersInRange = playersInRange.filter(p => p.CurHP > 0);

        console.log(`直接瞄準 ${targetPlayer.name}: 距離=${directDistance}, 範圍內玩家數=${alivePlayersInRange.length}`);

        return {
            canAttack: true,
            castPosition: casterPosition,
            targetPosition: targetPosition,
            playersHit: alivePlayersInRange.length
        };
    }

    // 無法直接瞄準，嘗試找到一個施法位置使得玩家在法術範圍內
    console.log(`無法直接瞄準 ${targetPlayer.name} (距離=${directDistance} > 施法距離=${distance}), 尋找間接攻擊位置...`);

    let bestResult = { canAttack: false, playersHit: 0 };

    // 檢查施法距離內的所有可能目標位置
    for (let row = 0; row < controlLayer[currentLevel].size.rows; row++) {
        for (let col = 0; col < controlLayer[currentLevel].size.cols; col++) {
            const potentialTargetPos = row * controlLayer[currentLevel].size.cols + col;

            // 檢查這個位置是否在施法距離內
            const distanceToTarget = calculateManhattanDistance(casterPosition, potentialTargetPos);
            if (distanceToTarget <= distance) {
                // 檢查目標玩家是否在這個位置的法術範圍內
                const playersInRange = getPlayersInMagicRange(potentialTargetPos, range);
                const isTargetInRange = playersInRange.some(p => p.Position === targetPosition && p.CurHP > 0);

                if (isTargetInRange) {
                    const alivePlayersInRange = playersInRange.filter(p => p.CurHP > 0);

                    if (alivePlayersInRange.length > bestResult.playersHit) {
                        bestResult = {
                            canAttack: true,
                            castPosition: casterPosition,
                            targetPosition: potentialTargetPos,
                            playersHit: alivePlayersInRange.length
                        };

                        console.log(`找到更好的間接攻擊位置: 目標位置=${potentialTargetPos}, 可命中玩家數=${alivePlayersInRange.length}`);
                    }
                }
            }
        }
    }

    return bestResult;
}

// 執行敵人法術行動
async function executeEnemyMagicAction(enemy, enemyIndex, magicAction) {
    console.log(`敵人 ${enemyIndex} 執行法術行動: ${magicAction.magicName}`);

    const magic = magicAction.magic;
    const needMP = magic.NeedMP || 0;

    // 扣除MP
    const oldMP = enemy.CurMP;
    enemy.CurMP = Math.max(0, enemy.CurMP - needMP);
    console.log(`敵人 ${enemyIndex} 消耗MP: ${oldMP} → ${enemy.CurMP} (-${needMP})`);

    // 獲取目標列表
    let targets = [];
    const classify = magic.Classify || "";

    if (classify === "治癒") {
        // 治癒法術：獲取目標位置範圍內的友軍
        targets = getEnemiesInMagicRange(magicAction.targetPosition, magic.Range || 0);
        // 只保留活著的友軍，並使用即時血量數據
        targets = targets.filter(ally => {
            const isAlive = ally.CurHP > 0;
            const currentHP = ally.CurHP;
            const maxHP = ally.HP;
            console.log(`檢查治療目標 ${ally.name}: 即時血量 ${currentHP}/${maxHP}, 存活: ${isAlive}`);
            return isAlive;
        });
        console.log(`治癒法術目標位置: ${magicAction.targetPosition}, 範圍: ${magic.Range}, 找到存活目標: ${targets.length}`);

        // 顯示所有目標的即時血量狀態
        targets.forEach(ally => {
            const healthRatio = (ally.CurHP / ally.HP * 100).toFixed(1);
            console.log(`- 治療目標: ${ally.name} (${ally.CurHP}/${ally.HP}, ${healthRatio}%)`);
        });
    } else {
        // 攻擊法術：獲取範圍內的玩家
        targets = getPlayersInMagicRange(magicAction.targetPosition, magic.Range || 0);
        // 只保留活著的玩家，並使用即時血量數據
        targets = targets.filter(player => {
            const isAlive = player.CurHP > 0;
            const currentHP = player.CurHP;
            const maxHP = player.HP;
            console.log(`檢查攻擊目標 ${player.name}: 即時血量 ${currentHP}/${maxHP}, 存活: ${isAlive}`);
            return isAlive;
        });
        console.log(`攻擊法術目標位置: ${magicAction.targetPosition}, 範圍: ${magic.Range}, 找到存活目標: ${targets.length}`);
    }

    if (targets.length > 0) {
        console.log(`敵人法術 ${magicAction.magicName} 影響目標:`, targets.map(t => t.name));

        try {
            // 執行法術動畫和效果（使用MagicFun.js的系統）
            await playMagicAnimation(enemy, magicAction.magicName, targets, magicAction.targetPosition);
            console.log(`敵人法術 ${magicAction.magicName} 執行完成`);
        } catch (error) {
            console.error(`敵人法術 ${magicAction.magicName} 執行過程中發生錯誤:`, error);
        }
    } else {
        console.log(`敵人法術 ${magicAction.magicName} 沒有有效目標`);
    }
}


