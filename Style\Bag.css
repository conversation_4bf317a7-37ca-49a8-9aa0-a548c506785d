/*查看角色中的*/
@font-face {
    font-family: 'MyCustomFont';
    src: url('../public/myfont.ttf') format('truetype');
}

#curequip {
    display: flex;
    flex-direction: row;
    gap: 5px;
    padding: 5px 20px;
    justify-content: center;
}

.equipment-slot {
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    padding: 15px;
    width: 140px;
    background: #f0f0f0;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.equipment-slot::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.slot-title {
    color: #000;
    font-size: 23px;
    text-align: center;
    margin-bottom: 10px;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
    font-weight: 600;
    letter-spacing: 1px;
}

.slot-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.slot-content img {
    width: 105px;
    height: 105px;
    border-radius: 10px;
}

.slot-content div {
    color: #000;
    font-family: '標楷體', sans-serif;
    font-size: 22px;
    white-space: nowrap;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-align: center;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.5);
}



/*=========================================================================*/

.normal-sword-icon {
    border: 2px solid gray !important;
}

.normal2-sword-icon {
    border: 2px solid gray !important;
}

.purple-sword-icon {
    box-shadow: 0 0 5px purple,
        0 0 25px purple,
        0 0 50px purple,
        0 0 200px purple !important;
    border: 2px solid purple !important;
    animation: lightanddark 1.5s infinite ease-in-out !important;
}

.gold-sword-icon {
    box-shadow: 0 0 5px orange,
        0 0 25px orange,
        0 0 50px orange,
        0 0 200px orange !important;
    border: 2px solid orange !important;
    animation: lightanddark 1.5s infinite ease-in-out !important;
}

.normal-armor-icon {
    border: 2px solid gray !important;
}

.silver-armor-icon {
    border: 2px solid gray !important;
}

.brown-armor-icon {
    box-shadow: 0 0 5px rgb(96, 22, 22),
        0 0 25px rgb(96, 22, 22),
        0 0 50px rgb(96, 22, 22),
        0 0 200px rgb(96, 22, 22) !important;
    border: 2px solid rgb(96, 22, 22) !important;
    animation: lightanddark 1.5s infinite ease-in-out !important;
}

.green-armor-icon {
    box-shadow: 0 0 5px greenyellow,
        0 0 25px greenyellow,
        0 0 50px greenyellow,
        0 0 200px greenyellow !important;
    border: 3px solid greenyellow !important;
    animation: lightanddark2 1.5s infinite ease-in-out !important;
}

@keyframes lightanddark {
    0% {
        filter: brightness(1);
    }

    50% {
        filter: brightness(1.5);
    }

    100% {
        filter: brightness(1);
    }
}

@keyframes lightanddark2 {
    0% {
        filter: brightness(1);
    }

    50% {
        filter: brightness(1.2);
    }

    100% {
        filter: brightness(1);
    }
}

.normal-fitting-icon {
    border: 2px solid gray !important;
}

.demon-fitting-icon {
    box-shadow: 0 0 5px aqua,
        0 0 25px aqua,
        0 0 50px aqua,
        0 0 200px aqua !important;
    border: 2px solid aqua !important;
    animation: lightanddark2 1.5s infinite ease-in-out !important;
}

.clouded-ring-icon {
    box-shadow: 0 0 5px blue,
        0 0 25px blue,
        0 0 50px blue,
        0 0 200px blue !important;
    border: 2px solid blue !important;
    animation: lightanddark 1.5s infinite ease-in-out !important;
}

.Liangyi-fitting-icon {
    box-shadow:
        8px 8px 100px 10px orange,
        -8px -8px 10px 2px purple,
        5px 5px 15px 0px orange !important;
    border: 5px solid !important;
    border-left-color: purple !important;
    border-right-color: orange !important;
    border-top-color: purple !important;
    border-bottom-color: orange !important;
    animation: lightanddark 1.5s infinite ease-in-out !important;
}

.Eating-normal-icon {
    border: 2px solid gray !important;
}

.material-fire-icon {
    box-shadow: 0 0 5px orangered,
        0 0 25px orangered,
        0 0 50px orangered,
        0 0 200px orangered !important;
    border: 2px solid orangered !important;
    animation: lightanddark2 1.5s infinite ease-in-out !important;

}

/*======================================================================================================*/

#playerinfo_right_menus {
    width: 90%;
    height: 10%;
    box-shadow: 0 0 5px 0 black;
    border-radius: 5px;
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.right_menus_options {
    font-size: 30px;
    font-weight: 600;
    width: 45%;
    text-align: center;
    cursor: pointer;
    transition: 0.5s ease-in-out;
}

.right_menus_options:hover {
    transform: scale(1.1);
    transform-origin: center;
}

#inventory-container {
    width: 95%;
    border-radius: 10px;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
    height: 80%;
    box-sizing: border-box;
    overflow-y: auto;
}

.inventory-title {
    color: #000;
    font-size: 30px;
    text-align: center;
    margin-bottom: 15px;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
    font-weight: 600;
    letter-spacing: 1px;
}

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    width: 100%;
    gap: 5px;
    padding: 5px;
}

.inventory-slot {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 15px 5px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
    align-content: space-around;
    transition: all 0.3s ease;
    cursor: pointer;
}

.inventory-slot:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.inventory-slot.empty {
    background: rgba(0, 0, 0, 0.05);
    cursor: default;
}

.inventory-slot img {
    width: 110px;
    height: 110px;
    border-radius: 10px;
}

.inventory-slot .item-name {
    color: #000;
    font-family: '標楷體', sans-serif;
    font-size: 20px;
    text-align: center;
    font-weight: 600;
}

.inventory-slot .item-description {
    color: #666;
    font-size: 12px;
    text-align: center;
    display: none;
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: nowrap;
    z-index: 100;
}

.inventory-slot:hover .item-description {
    display: block;
}

#item-options-dialog {
    padding: 0;
    border: none;
    border-radius: 15px;
    background: rgba(247, 237, 172);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    margin: auto;
    border: 5px solid rgb(168, 105, 38);
}

#item-options-dialog::backdrop {
    background: rgba(0, 0, 0, 0.5);
}

.item-options-container {
    width: 400px;
    padding: 20px;
}

.item-options-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.item-options-header img {
    width: 80px;
    height: 80px;
    border-radius: 8px;
}

.item-options-title {
    font-size: 28px;
    font-weight: 600;
    color: #000;
    font-family: '標楷體', sans-serif;
}

.item-options-content {
    margin-bottom: 20px;
}

.item-description-full {
    color: #666;
    font-size: 20px;
    margin-bottom: 20px;
    line-height: 1.5;
    font-family: '微軟正黑體', sans-serif;
}

.item-options-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.item-options-buttons button {
    padding: 8px 20px;
    border: none;
    border-radius: 5px;
    background: #4dabf7;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: '微軟正黑體', sans-serif;
}

.item-options-buttons button:hover {
    background: #339af0;
    transform: translateY(-2px);
}

.item-options-footer {
    text-align: center;
    padding-top: 15px;
    border-top: 2px solid rgba(0, 0, 0, 0.1);
}

.item-options-footer button {
    padding: 8px 30px;
    border: none;
    border-radius: 5px;
    background: #868e96;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: '微軟正黑體', sans-serif;
}

.item-options-footer button:hover {
    background: #495057;
    transform: translateY(-2px);
}








/*操作選單中的*/
#optionsbag {
    position: absolute;
    top: -30px;
    width: 300px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    flex-direction: row !important;
    animation: appear 1s forwards;
}

#transferbtn {
    background-image: url('../icons/transferbtn.png');
    background-size: contain;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: all 0.3s;
    transform-origin: center;
}

#useitembtn {
    background-image: url('../icons/useitembtn.png');
    background-size: contain;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: all 0.3s;
    transform-origin: center;
}

#dropitembtn {
    background-image: url('../icons/dropitembtn.png');
    background-size: contain;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: all 0.3s;
    transform-origin: center;
}

#itemdialog {
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    background: none;
    outline: none;
    border: rgb(168, 105, 38);
}

#itemdialog::backdrop {
    background: rgba(0, 0, 0, 0.5);
}

#itemwindow {
    width: 800px;
    height: 600px;
    background: rgb(247, 231, 173);
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;
    justify-self: flex-start;
    gap: 10px;
    overflow: hidden;
}

#itemtoparea {
    width: 100%;
    height: 15%;
    background: rgb(247, 231, 173);
    display: flex;
    display: flex;
    justify-content: center;
    align-items: center;
}

#itemtitle {
    font-size: 30px;
    color: rgb(168, 105, 38);
    font-weight: 600;
    letter-spacing: 2px;
    text-align: center;
    width: 100%;
}

#itemclosebtn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background-image: url('../icons/cancel.png');
    background-size: contain;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: all 0.3s;
    transform-origin: center;
}

#itemconatainer {
    width: 95%;
    height: 80%;
    background: rgb(247, 231, 173);
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid rgb(168, 105, 38);
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
    gap: 10px;
    overflow: scroll
}

#itemconatainer::webkit-scrollbar {
    width: 5px;
}

#itemconatainer::webkit-scrollbar-thumb {
    background: rgb(168, 105, 38);
    border-radius: 5px;
}

#itemconatainer::webkit-scrollbar-track {
    background: rgb(247, 231, 173);
    border-radius: 5px;
}


#itemlist {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    width: 95%;
    height: 95%;
    padding: 8px;
    display: flex;
    justify-content: start;
    align-items: start;
    gap: 5px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.item {
    width: 140px;
    height: 140px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: 0.3s ease;
}

.item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.item img {
    width: 100px;
    height: 100px;
}

.itemname {
    font-size: 16px;
    color: rgb(168, 105, 38);
    font-weight: 600;
    letter-spacing: 2px;
    text-align: center;
}


/*玩家對背包操作的顯示範圍(注意：這裡每增加新的角色資料都要在這裡添加樣式)*/
.playercanuseitem0 {
    background-color: rgba(30, 216, 30, .6);
    cursor: pointer;
    animation: lightanddark3 1.5s infinite alternate;
}

.playercanuseitem1 {
    background-color: rgba(30, 216, 30, .6);
    cursor: pointer;
    animation: lightanddark3 1.5s infinite alternate;
}

.playercanuseitem2 {
    background-color: rgba(30, 216, 30, .6);
    cursor: pointer;
    animation: lightanddark3 1.5s infinite alternate;
}

.playeritemrange0 {
    background-color: rgba(30, 216, 30, .6);
    cursor: pointer;
    border: 2px solid rgba(0, 0, 0, 0.5);
    animation: lightanddark4 1.5s infinite alternate;
}

.playeritemrange1 {
    background-color: rgba(30, 216, 30, .6);
    cursor: pointer;
    border: 2px solid rgba(0, 0, 0, 1);
    animation: lightanddark4 1.5s infinite alternate;
}

.playeritemrange2 {
    background-color: rgba(30, 216, 30, .6);
    cursor: pointer;
    border: 2px solid rgba(0, 0, 0, 1);
    animation: lightanddark4 1.5s infinite alternate;
}



#transferdialog {
    position: absolute;
    top: 69%;
    width: 95%;
    height: 30%;
    background-image: url('../Public/msg.png');
    background-repeat: no-repeat;
    background-size: cover;
    font-size: 25px;
    color: rgb(168, 105, 38);
    font-weight: 600;
    letter-spacing: 2px;
    animation: appear 1s forwards;
    overflow: visible;
}

#transferMsg {
    position: relative;
    left: 5%;
    top: 10%;
    width: 70%;
    height: 90%;
    display: flex;
    justify-content: start;
}

.stats-container-ability {
    position: relative;
    left: 2.5%;
    width: 550px;
    height: 450px;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    gap: 20px;
}

.stat-item-ability {
    width: 15%;
    height: 90%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    border-radius: 10px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
}

.ability-progress {
    width: 40%;
    height: 80%;
    background-color: #eeeeee66;
    border-radius: 5px;
    display: flex;
    overflow: hidden;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    /* 內陰影增加立體感 */
    transform: rotateX(180deg);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.ability-progress-cur {
    width: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
    background-size: 100% 100%;
    filter: brightness(1.3);
    border-bottom: 5px solid #ddd;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
}

.stat-label {
    white-space: nowrap;
    font-size: 25px;
    color: rgb(168, 105, 38);
    font-weight: 600;
    letter-spacing: 2px;
}

.empty-message {
    position: relative;
    left: 2.5%;
    font-size: 40px;
    text-align: center;
    color: rgb(168, 105, 38);
    font-weight: 600;
    letter-spacing: 2px;
    width: 550px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stats-container-resistance {
    position: relative;
    left: 2.5%;
    width: 550px;
    height: 450px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}


.stat-item-resistance {
    position: relative;
    width: 95%;
    height: 15%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
}

.resistance-progress {
    position: relative;
    width: 80%;
    height: 40%;
    background-color: #eeeeee66;
    border-radius: 5px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

.resistance-progress-cur {
    position: relative;
    max-width: 100%;
    height: 100%;
    /*background-color: rgb(241, 62, 62);*/
    background-color: #339af0;
    border-radius: 5px;
    border: 3px solid #ddd;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
}














@keyframes lightanddark3 {

    0% {
        background-color: rgba(30, 216, 30, .4);
    }

    100% {
        background-color: rgb(30, 216, 30, .6);
    }
}

@keyframes lightanddark4 {

    0% {
        background-color: rgba(30, 216, 30, .4);
    }

    100% {
        background-color: rgb(30, 216, 30, .6);
    }
}

@keyframes floatUp {
    0% {
        transform: translateY(0);
        opacity: 1;
    }

    100% {
        transform: translateY(-50px);
        opacity: 0;
    }
}

@keyframes shopappear {
    0% {
        transform: scale(0);
        opacity: 0;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}