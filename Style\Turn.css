.turntext {
    position: absolute;
    bottom: 0%;
    left: 55%;
    width: 610px;
    height: 85px;
    z-index: 100;
    display: flex;
    justify-content: start;
    align-items: center;
    font-size: 55px;
    color: white;
    letter-spacing: 5px;
    font-family: '標楷體';
    padding-left: 50px;
    text-shadow: 0 0 20px #000;
    box-shadow: 0 0 20px #000;
    border: none;
}

@keyframes turnappear {
    0% {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, .3) 100%);
        transform: translateY(150%);
    }

    100% {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, .3) 100%);
        transform: translateY(0%);
        animation-delay: 1s;
    }
}

#questiondialog {
    width: 100%;
    height: 100%;
    background: none;
    outline: none;
    border: none;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

#questiondialog::backdrop {
    backdrop-filter: blur(10px);
}

#questiondialog_inner {
    width: 35%;
    height: 60%;
    background: rgb(247, 231, 173);
    border: 5px solid rgb(165, 90, 24);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

#question {
    width: 80%;
    height: 50%;
    font-size: 35px;
    font-family: '微軟正黑體';
    font-weight: 600;
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgb(168, 105, 38);
}

#questionbtn {
    width: 80%;
    height: 35%;
    display: flex;
    justify-content: center;
    align-items: center;
}

#questionbtn div {
    width: 100px;
    height: 60px;
    border-radius: 5px;
    background: rgb(247, 231, 173);
    border: 3px solid rgb(165, 90, 24);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 25px;
    color: rgb(168, 105, 38);
    font-weight: 600;
    font-family: '微軟正黑體';
    cursor: pointer;
    margin: 0 10px;
    transition: all 0.3s;
}

#questionbtn div:hover {
    background: rgb(165, 90, 24);
    color: #eee;
}

@keyframes turndisappear {
    0% {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, .3) 100%);
        transform: translateY(0%);
    }

    100% {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, .3) 100%);
        transform: translateY(150%);
    }
}

