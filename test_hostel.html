<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客棧場景測試</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft JhengHei', <PERSON>l, sans-serif;
            background: #222;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            padding: 10px 20px;
            margin: 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-result {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
        }
        .info-panel {
            position: fixed;
            right: 20px;
            top: 20px;
            width: 200px;
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🏨 客棧場景測試頁面</h1>
        
        <div class="test-result">
            <h3>測試狀態</h3>
            <div id="testStatus">正在檢查...</div>
        </div>

        <div>
            <button class="test-button" onclick="testHostelSceneClass()">測試 HostelScene 類別</button>
            <button class="test-button" onclick="testCreateHostelScene()">測試創建客棧場景</button>
            <button class="test-button" onclick="testSceneManager()">測試場景管理器</button>
            <button class="test-button" onclick="simulateHostelEntry()">模擬進入客棧</button>
            <button class="test-button" onclick="testSidebarRestore()">測試 Sidebar 恢復</button>
            <button class="test-button" onclick="testHostelSaveLoad()">測試客棧存檔功能</button>
            <button class="test-button" onclick="testDynamicMenu()">測試動態選單創建</button>
            <button class="test-button" onclick="testMenuAnimations()">測試選單動畫</button>
        </div>

        <div id="testResults"></div>
    </div>

    <div class="info-panel">
        <h4>客棧測試</h4>
        <p>客棧有獨立的選單系統</p>
        <p>會正確保存和恢復 sidebar 狀態</p>
    </div>

    <!-- 模擬原本的 sidebar -->
    <div id="sidebar" style="position: fixed; left: 20px; top: 20px; width: 150px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; z-index: 1000;">
        <h4>原本的 Sidebar</h4>
        <p>這是模擬的原本 sidebar</p>
        <button style="width: 100%; padding: 5px; margin: 2px 0;">按鈕1</button>
        <button style="width: 100%; padding: 5px; margin: 2px 0;">按鈕2</button>
    </div>

    <!-- 載入必要的腳本 -->
    <script src="APP/HostelScene.js"></script>
    <script src="APP/Init.js"></script>

    <script>
        // 測試函數
        function addTestResult(title, result, isError = false) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isError ? 'error' : ''}`;
            resultDiv.innerHTML = `<h4>${title}</h4><pre>${result}</pre>`;
            resultsDiv.appendChild(resultDiv);
        }

        function testHostelSceneClass() {
            try {
                const result = [];
                result.push(`typeof HostelScene: ${typeof HostelScene}`);
                result.push(`typeof window.HostelScene: ${typeof window.HostelScene}`);
                
                if (typeof HostelScene !== 'undefined') {
                    result.push('✅ HostelScene 類別已定義');
                    result.push(`HostelScene.name: ${HostelScene.name}`);
                    result.push(`HostelScene.prototype: ${Object.getOwnPropertyNames(HostelScene.prototype)}`);
                } else {
                    result.push('❌ HostelScene 類別未定義');
                }

                addTestResult('HostelScene 類別測試', result.join('\n'));
            } catch (error) {
                addTestResult('HostelScene 類別測試', `錯誤: ${error.message}`, true);
            }
        }

        function testCreateHostelScene() {
            try {
                if (typeof HostelScene === 'undefined') {
                    throw new Error('HostelScene 類別未定義');
                }

                const hostelScene = new HostelScene(0, {}, 'test');
                const result = [];
                result.push('✅ HostelScene 實例創建成功');
                result.push(`hostelIndex: ${hostelScene.hostelIndex}`);
                result.push(`previousScene: ${hostelScene.previousScene}`);
                result.push(`isLoaded: ${hostelScene.isLoaded}`);
                result.push(`isRunning: ${hostelScene.isRunning}`);

                addTestResult('創建 HostelScene 實例', result.join('\n'));
            } catch (error) {
                addTestResult('創建 HostelScene 實例', `錯誤: ${error.message}`, true);
            }
        }

        function testSceneManager() {
            try {
                const result = [];
                result.push(`typeof sceneManager: ${typeof sceneManager}`);
                
                if (typeof sceneManager !== 'undefined') {
                    result.push('✅ sceneManager 已定義');
                    result.push(`switchToHostel 方法: ${typeof sceneManager.switchToHostel}`);
                } else {
                    result.push('❌ sceneManager 未定義');
                }

                addTestResult('場景管理器測試', result.join('\n'));
            } catch (error) {
                addTestResult('場景管理器測試', `錯誤: ${error.message}`, true);
            }
        }

        function simulateHostelEntry() {
            try {
                if (typeof HostelScene === 'undefined') {
                    throw new Error('HostelScene 類別未定義，無法模擬');
                }

                // 清理之前的客棧場景
                const existingHostel = document.getElementById('hostelContainer');
                if (existingHostel) {
                    existingHostel.remove();
                }

                // 創建模擬的場景管理器
                if (typeof sceneManager === 'undefined') {
                    window.sceneManager = {
                        currentScene: null,
                        sceneType: null,
                        previousScene: null,
                        gameData: { playerData: {} },
                        isTransitioning: false,

                        switchToHostel: function(hostelIndex, previousScene) {
                            console.log(`模擬切換到客棧: ${hostelIndex}, 來源: ${previousScene}`);
                            this.previousScene = this.sceneType;
                            this.sceneType = 'hostel';

                            const hostelScene = new HostelScene(hostelIndex, this.gameData.playerData, previousScene);
                            this.currentScene = hostelScene;
                            hostelScene.init();

                            return true;
                        },

                        cleanup: function() {
                            if (this.currentScene && this.currentScene.cleanup) {
                                this.currentScene.cleanup();
                            }
                            this.currentScene = null;
                        }
                    };
                }

                const success = sceneManager.switchToHostel(0, 'camp');
                addTestResult('模擬進入客棧', success ? '✅ 成功進入客棧場景\n📝 注意：客棧有獨立的選單系統，不會影響原本的 sidebar' : '❌ 進入客棧失敗');

                // 添加清理按鈕
                setTimeout(() => {
                    const cleanupButton = document.createElement('button');
                    cleanupButton.textContent = '清理客棧場景';
                    cleanupButton.className = 'test-button';
                    cleanupButton.style.background = '#f44336';
                    cleanupButton.onclick = () => {
                        if (sceneManager && sceneManager.cleanup) {
                            sceneManager.cleanup();
                            addTestResult('清理場景', '✅ 客棧場景已清理');
                        }
                    };
                    document.querySelector('.test-container').appendChild(cleanupButton);
                }, 1000);

            } catch (error) {
                addTestResult('模擬進入客棧', `錯誤: ${error.message}`, true);
            }
        }

        function testSidebarRestore() {
            try {
                const sidebar = document.getElementById('sidebar');
                if (!sidebar) {
                    throw new Error('找不到 sidebar 元素');
                }

                const result = [];

                // 記錄初始狀態
                const initialState = {
                    display: sidebar.style.display || getComputedStyle(sidebar).display,
                    visibility: sidebar.style.visibility || getComputedStyle(sidebar).visibility
                };
                result.push(`初始 sidebar 狀態: display=${initialState.display}, visibility=${initialState.visibility}`);

                // 創建客棧場景（會隱藏 sidebar）
                if (typeof HostelScene === 'undefined') {
                    throw new Error('HostelScene 類別未定義');
                }

                const hostelScene = new HostelScene(0, {}, 'camp');
                hostelScene.saveSidebarState();

                // 檢查 sidebar 是否被隱藏
                const hiddenState = {
                    display: sidebar.style.display,
                    visibility: sidebar.style.visibility
                };
                result.push(`隱藏後 sidebar 狀態: display=${hiddenState.display}, visibility=${hiddenState.visibility}`);

                // 恢復 sidebar
                hostelScene.restoreSidebar();

                // 檢查 sidebar 是否被恢復
                const restoredState = {
                    display: sidebar.style.display || getComputedStyle(sidebar).display,
                    visibility: sidebar.style.visibility || getComputedStyle(sidebar).visibility
                };
                result.push(`恢復後 sidebar 狀態: display=${restoredState.display}, visibility=${restoredState.visibility}`);

                // 驗證是否正確恢復
                const isRestored = (restoredState.display !== 'none') && (restoredState.visibility !== 'hidden');
                result.push(isRestored ? '✅ Sidebar 成功恢復' : '❌ Sidebar 恢復失敗');

                addTestResult('Sidebar 恢復測試', result.join('\n'));

            } catch (error) {
                addTestResult('Sidebar 恢復測試', `錯誤: ${error.message}`, true);
            }
        }

        function testHostelSaveLoad() {
            try {
                if (typeof HostelScene === 'undefined') {
                    throw new Error('HostelScene 類別未定義');
                }

                const result = [];

                // 創建客棧場景實例
                const hostelScene = new HostelScene(0, { testData: 'test' }, 'camp');
                result.push('✅ 客棧場景實例創建成功');

                // 測試獲取當前狀態
                const currentState = hostelScene.getCurrentHostelState();
                result.push(`✅ 獲取當前狀態成功: hostelIndex=${currentState.hostelIndex}, previousScene=${currentState.previousScene}`);

                // 測試存檔
                const saveSuccess = hostelScene.saveHostelState(0);
                result.push(saveSuccess ? '✅ 存檔成功' : '❌ 存檔失敗');

                // 測試獲取槽位信息
                const slotInfo = hostelScene.getHostelSlotInfo(0);
                if (slotInfo) {
                    result.push(`✅ 獲取槽位信息成功: hostelIndex=${slotInfo.hostelIndex}, timestamp=${new Date(slotInfo.timestamp).toLocaleString()}`);
                } else {
                    result.push('❌ 獲取槽位信息失敗');
                }

                // 測試讀檔
                const loadedState = hostelScene.loadHostelState(0);
                if (loadedState) {
                    result.push(`✅ 讀檔成功: hostelIndex=${loadedState.hostelIndex}, previousScene=${loadedState.previousScene}`);
                } else {
                    result.push('❌ 讀檔失敗');
                }

                // 測試存檔界面創建
                try {
                    // 先初始化客棧場景
                    hostelScene.createHostelContainer();
                    hostelScene.showSaveHostelDialog();
                    result.push('✅ 存檔界面創建成功');

                    // 清理
                    setTimeout(() => {
                        const saveDialog = document.getElementById('save-hostel-dialog');
                        if (saveDialog) {
                            saveDialog.close();
                            if (saveDialog.parentNode) {
                                saveDialog.parentNode.removeChild(saveDialog);
                            }
                        }
                        hostelScene.cleanup();
                    }, 1000);
                } catch (error) {
                    result.push(`❌ 存檔界面創建失敗: ${error.message}`);
                }

                addTestResult('客棧存檔功能測試', result.join('\n'));

            } catch (error) {
                addTestResult('客棧存檔功能測試', `錯誤: ${error.message}`, true);
            }
        }

        // 頁面載入完成後執行初始檢查
        window.addEventListener('load', function() {
            const statusDiv = document.getElementById('testStatus');
            const status = [];
            
            status.push(`HostelScene: ${typeof HostelScene !== 'undefined' ? '✅' : '❌'}`);
            status.push(`window.HostelScene: ${typeof window.HostelScene !== 'undefined' ? '✅' : '❌'}`);
            
            statusDiv.innerHTML = status.join('<br>');
            
            // 自動執行基本測試
            setTimeout(() => {
                testHostelSceneClass();
            }, 500);
        });

        // 測試動態選單創建
        function testDynamicMenu() {
            try {
                if (typeof HostelScene === 'undefined') {
                    throw new Error('HostelScene 類別未定義');
                }

                // 測試不同來源場景的選單
                const testScenes = [
                    { previousScene: null, description: '直接進入' },
                    { previousScene: 'camp', description: '從營地進入' },
                    { previousScene: 'level', description: '從關卡進入' }
                ];

                const results = [];
                testScenes.forEach(test => {
                    const hostelScene = new HostelScene(0, {}, test.previousScene);
                    const menuOptions = hostelScene.generateMenuOptions();
                    results.push(`${test.description}: ${menuOptions.length} 個選項`);
                    results.push(`  選項: ${menuOptions.map(opt => opt.text).join(', ')}`);
                });

                addTestResult('動態選單創建測試', results.join('\n'));
            } catch (error) {
                addTestResult('動態選單創建測試', `錯誤: ${error.message}`, true);
            }
        }

        function testMenuAnimations() {
            try {
                if (typeof HostelScene === 'undefined') {
                    throw new Error('HostelScene 類別未定義');
                }

                const hostelScene = new HostelScene(0, {}, 'camp');

                // 模擬初始化
                hostelScene.hostelContainer = document.createElement('div');
                hostelScene.hostelContainer.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100vw;
                    height: 100vh;
                    z-index: 1000;
                    background: rgba(0,0,0,0.5);
                `;
                document.body.appendChild(hostelScene.hostelContainer);

                // 測試選單開啟
                hostelScene.openHostelMenu();

                const result = [];
                result.push('✅ 選單動畫測試開始');
                result.push('選單應該會出現在畫面中央');
                result.push('3秒後自動關閉...');

                // 3秒後自動關閉
                setTimeout(() => {
                    hostelScene.closeHostelMenu();
                    setTimeout(() => {
                        if (hostelScene.hostelContainer.parentNode) {
                            hostelScene.hostelContainer.remove();
                        }
                        addTestResult('選單動畫測試', '✅ 選單動畫測試完成');
                    }, 500);
                }, 3000);

                addTestResult('選單動畫測試', result.join('\n'));
            } catch (error) {
                addTestResult('選單動畫測試', `錯誤: ${error.message}`, true);
            }
        }

        // 監聽 HostelScene 準備就緒事件
        document.addEventListener('hostelSceneReady', function(event) {
            console.log('HostelScene 準備就緒事件觸發:', event.detail);
            addTestResult('事件監聽', '✅ HostelScene 準備就緒事件已觸發');
        });
    </script>
</body>
</html>
