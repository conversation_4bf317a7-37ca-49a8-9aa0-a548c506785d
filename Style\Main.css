* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    user-select: none;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

@font-face {
    font-family: 圓體;
    src: url(https://cdn.jsdelivr.net/gh/max32002/maruko-gothic@1.005/webfont/CJK%20JP/MarukoGothicCJKjp-Regular.woff2) format("woff2");
}

body {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

#Container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: green;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

#GameScreen {
    width: 95vw;
    height: 100vh;
    background-color: #000;
}

#uidialog {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 94.5%;
    height: 1000px;
    background-image: url(../Public/UI.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    outline: none;
    border: none;
    padding: 0;
    margin: 0;
    background-color: rgba(0, 0, 0, 0.5);
    background-blend-mode: multiply;
}

#uidialog::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

#uimenu {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 270px;
    height: 310px;
    background-image: url(../Public/UImenu.png);
    color: rgb(168, 105, 38);
    background-size: contain;
    background-repeat: no-repeat;
    animation: uiappear 0.5s linear forwards;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.uiitem {
    font-size: 30px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 12px;
    cursor: pointer;
    letter-spacing: 2px;
    transition: .3s;
}

.uiitem:hover {
    color: rgba(168, 105, 38, 0.573);
}


#savegame-dialog::backdrop {
    background-color: rgba(0, 0, 0, 1);
}

@keyframes uiappear {
    from {
        opacity: 0;
        top: 100%;
    }

    to {
        opacity: 1;
        top: 50%
    }

    ;
}