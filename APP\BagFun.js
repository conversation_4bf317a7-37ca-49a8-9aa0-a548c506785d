/*========================= 背包物品操作細項功能： 為Main.js 、 Players.js 的細項 ============================== */


/*======================== 以下為 "查看角色" 內物品功能中的操作選項  =================================== */

// 添加物品操作選單函數(不要修改，不要修改)
function showItemOptions(itemIndex, itemType) {
    // 獲取當前查看的角色
    const player = gameplayers.find(p => p === currentViewedPlayer);
    if (!player) {
        return;
    }
    //抓取不屬於medicine類型的物品
    const item = player.Inventory[itemIndex];

    if (!item) {
        console.error("無法找到指定的物品");
        return;
    }

    // 根據物品類型和角色是否可操控來決定顯示的按鈕
    let buttons = '';
    if (!player["是否電腦操作"]) {  // 如果是玩家可操控的角色
        if (itemType === 'medicine') {
            buttons = `
            <button onclick="dropItem(${itemIndex})" style="background:red!important;font-weight:600;font-size:20px;">丟棄</button>
        `;
        }
        if (item.Who && item.Who.includes(player.name)) {
            buttons = `
            <button onclick="equipItem(${itemIndex})" style="background:lightblue !important;font-weight:600;font-size:20px;color:#222;">裝備</button>
            <button onclick="dropItem(${itemIndex})" style="background:red !important;font-weight:600;font-size:20px;">丟棄</button>
        `;

        } else {
            buttons = `
            <button onclick="dropItem(${itemIndex})" style="background:red !important;font-weight:600;font-size:20px;">丟棄</button>
        `;

        }
    }




    // 創建操作選單對話框
    const itemDialog = document.createElement('dialog');
    itemDialog.id = 'item-options-dialog';
    itemDialog.innerHTML = `
        <div class="item-options-container">
            <div class="item-options-header">
                <img src="${item.image}" alt="${item.name}" class="${item.sytles || ''}">
                <div class="item-options-title">${item.name}</div>
            </div>
            <div class="item-options-content">
                <div class="item-description-full">${item.description}</div>
                <div class="item-options-buttons">
                    ${buttons}
                </div>
            </div>
            <div class="item-options-footer">
                <button onclick="closeItemOptions()">關閉</button>
            </div>
        </div>
    `;

    document.body.appendChild(itemDialog);
    itemDialog.showModal();
}

// 關閉物品操作選單
function closeItemOptions() {
    const dialog = document.getElementById('item-options-dialog');
    if (dialog) {
        dialog.close();
        dialog.remove();
    }
}

// 裝備物品
function equipItem(itemIndex) {
    const player = gameplayers.find(p => p === currentViewedPlayer);
    if (!player) {
        console.error("無法找到當前選中的角色");
        return;
    }


    const item = player.Inventory[itemIndex];
    console.log("裝備物品：", item);

    if (!item) {
        console.error("無法找到指定的物品");
        return;
    }

    // 找到原始背包中該物品的索引
    const originalIndex = player.Inventory.findIndex(invItem => invItem === item);
    if (originalIndex === -1) {
        console.error("無法在背包中找到該物品");
        return;
    }

    // 根據物品類型裝備到對應欄位
    if (item.type === 'weapon') {
        // 如果已有裝備，先放回背包
        if (player.Equipment.Weapon) {
            player.Inventory.push(player.Equipment.Weapon);
        }
        player.Equipment.Weapon = item;
    } else if (item.type === 'armor') {
        if (player.Equipment.Armor) {
            player.Inventory.push(player.Equipment.Armor);
        }
        player.Equipment.Armor = item;
    } else if (item.type === 'accessory') {
        if (player.Equipment.Fitting) {
            player.Inventory.push(player.Equipment.Fitting);
        }
        player.Equipment.Fitting = item;
    }

    // 從背包中移除該物品（使用原始索引）
    player.Inventory.splice(originalIndex, 1);

    // 更新顯示
    closeItemOptions();
    lookrole(player);
}

// 丟棄物品
function dropItem(itemIndex) {
    const player = gameplayers.find(p => p === currentViewedPlayer);
    if (!player) {
        console.error("無法找到當前選中的角色");
        return;
    }


    const item = player.Inventory[itemIndex];

    if (!item) {
        console.error("無法找到指定的物品");
        return;
    }

    // 找到原始背包中該物品的索引
    const originalIndex = player.Inventory.findIndex(invItem => invItem === item);
    if (originalIndex === -1) {
        console.error("無法在背包中找到該物品");
        return;
    }

    // 從背包中移除物品（使用原始索引）
    if (window.confirm(`確定要丟棄【${item.name}】嗎?`)) {
        player.Inventory.splice(originalIndex, 1);
    }
    // 更新顯示
    closeItemOptions();
    lookrole(player);
}

// 更新裝備欄位顯示
function updateEquipmentDisplay(player) {
    // 更新武器
    const weaponImg = document.getElementById('weapon-img');
    const weaponName = document.getElementById('weapon-name');
    if (player.Equipment.Weapon) {
        weaponImg.src = player.Equipment.Weapon.image;
        weaponName.textContent = player.Equipment.Weapon.name;
    } else {
        weaponImg.src = '';
        weaponName.textContent = '無';
    }

    // 更新防具
    const armorImg = document.getElementById('armor-img');
    const armorName = document.getElementById('armor-name');
    if (player.Equipment.Armor) {
        armorImg.src = player.Equipment.Armor.image;
        armorName.textContent = player.Equipment.Armor.name;
    } else {
        armorImg.src = '';
        armorName.textContent = '無';
    }

    // 更新飾品
    const fittingImg = document.getElementById('fitting-img');
    const fittingName = document.getElementById('fitting-name');
    if (player.Equipment.Fitting) {
        fittingImg.src = player.Equipment.Fitting.image;
        fittingName.textContent = player.Equipment.Fitting.name;
    } else {
        fittingImg.src = '';
        fittingName.textContent = '無';
    }
}

/*======================== 以下為 "移動完後操作選單中" 內物品功能中的操作選項  =================================== */

//使用物品動畫
async function animationforeatting(players, item) {
    return new Promise(async (resolve) => {
        // 播放物品音效
        if (item.sound) {
            const itemSound = operates.playSound(item.sound);
            if (!itemSound) {
                console.warn("播放物品音效失敗:", item.sound);
            }

            // 音效播放完成後移除
            itemSound.onended = function () {
                itemSound.remove();
            };
        }



        // 為每個受影響的玩家播放動畫
        const animationPromises = players.map(player => {
            return new Promise(async (playerResolve) => {
                // 找到玩家在canvas中的物件
                const playerObj = mapObjects.find(obj =>
                    obj.type === 'player' &&
                    gameplayers[obj.playerIndex] &&
                    gameplayers[obj.playerIndex].Position === player.Position
                );

                if (!playerObj) {
                    console.warn(`找不到玩家 ${player.name} 的canvas物件`);
                    playerResolve();
                    return;
                }

                // 檢查物品是否有動畫圖片陣列
                if (item.animates && Array.isArray(item.animates) && item.animates.length > 0) {
                    // 載入動畫圖片
                    const animationImages = [];
                    for (let i = 0; i < item.animates.length; i++) {
                        try {
                            const img = await preloadImage(item.animates[i]);
                            animationImages.push(img);
                        } catch (error) {
                            console.warn(`載入物品動畫圖片失敗: ${item.animates[i]}`, error);
                        }
                    }

                    if (animationImages.length > 0) {
                        // 播放物品動畫
                        await playItemAnimation(playerObj, animationImages, item);
                    }
                }

                // 顯示恢復數值
                showRecoveryNumbers(player, item);

                playerResolve();
            });
        });

        // 等待所有玩家的動畫完成
        await Promise.all(animationPromises);

        // 額外等待一段時間讓數值顯示完成
        setTimeout(() => {
            resolve();
        }, 2000);
    });
}

// 播放物品動畫 - 使用獨立圖層避免與角色渲染衝突
async function playItemAnimation(playerObj, animationImages, item) {
    return new Promise((resolve) => {
        let currentFrame = 0;
        const frameInterval = 100; // 每100ms切換一幀

        // 創建效果動畫物件，添加到mapObjects中作為獨立圖層
        const effectObj = {
            type: 'effect',
            gridX: playerObj.gridX,
            gridY: playerObj.gridY,
            img: animationImages[0],
            width: playerObj.width + 40,
            height: playerObj.height + 50,
            zIndex: playerObj.zIndex + 0.1, // 稍微高於角色，確保在角色上方
            isEffectAnimation: true,
            currentFrame: 0,
            animationImages: animationImages,
            frameInterval: frameInterval,
            lastFrameTime: performance.now(),
            // 濾鏡效果屬性
            useFilter: true,
            brightness: 1.5, // 亮度增強到1.5倍
            contrast: 1.1,   // 對比度增強到1.1倍
            saturate: 1.2    // 飽和度增強到1.2倍
        };

        // 添加效果物件到mapObjects
        mapObjects.push(effectObj);

        function playNextFrame() {
            if (currentFrame < animationImages.length) {
                // 更新效果動畫幀
                effectObj.img = animationImages[currentFrame];
                effectObj.currentFrame = currentFrame;
                render(); // 重新渲染

                currentFrame++;
                setTimeout(playNextFrame, frameInterval);
            } else {
                // 動畫播放完成，移除效果物件
                const effectIndex = mapObjects.findIndex(obj => obj === effectObj);
                if (effectIndex !== -1) {
                    mapObjects.splice(effectIndex, 1);
                }
                render();
                resolve();
            }
        }

        playNextFrame();
    });
}

// 清理所有效果動畫物件
function clearAllEffectAnimations() {
    for (let i = mapObjects.length - 1; i >= 0; i--) {
        if (mapObjects[i].type === 'effect') {
            mapObjects.splice(i, 1);
        }
    }
    render();
}

// 計算物品作用範圍內的所有玩家
function getPlayersInItemRange(centerPosition, item) {
    const affectedPlayers = [];
    const itemRange = item.range || 0;

    if (itemRange === 0) {
        // 範圍為0，只影響中心位置的玩家
        const playerAtCenter = gameplayers.find(p => p.Position === centerPosition);
        if (playerAtCenter) {
            affectedPlayers.push(playerAtCenter);
        }
    } else {
        // 使用BFS計算範圍內的所有位置
        const affectedPositions = calculateItemRangePositions(centerPosition, itemRange);

        // 找出這些位置上的所有玩家
        affectedPositions.forEach(position => {
            const playerAtPosition = gameplayers.find(p => p.Position === position);
            if (playerAtPosition) {
                affectedPlayers.push(playerAtPosition);
            }
        });
    }

    console.log(`物品 ${item.name} 作用範圍內找到 ${affectedPlayers.length} 個玩家:`,
                affectedPlayers.map(p => p.name));

    return affectedPlayers;
}

// 計算物品作用範圍內的所有位置
function calculateItemRangePositions(centerPosition, range) {
    const positions = new Set();
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    const queue = [{ position: centerPosition, distance: 0 }];
    const visited = new Set();
    visited.add(centerPosition);
    positions.add(centerPosition);

    while (queue.length > 0) {
        const { position, distance } = queue.shift();

        if (distance < range) {
            directions.forEach(direction => {
                const currentX = position % controlLayer[currentLevel].size.cols;
                const currentY = Math.floor(position / controlLayer[currentLevel].size.cols);
                const newX = currentX + direction.x;
                const newY = currentY + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                // 檢查邊界
                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols &&
                    newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    if (!visited.has(newPosition)) {
                        visited.add(newPosition);
                        positions.add(newPosition);
                        queue.push({ position: newPosition, distance: distance + 1 });
                    }
                }
            });
        }
    }

    return Array.from(positions);
}

// 顯示恢復數值 - 使用 canvas 繪製
function showRecoveryNumbers(player, item) {
    // 數字轉中文函數
    function numberToChinese(num) {
        const chineseNumbers = ['〇', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        return num.toString().split('').map(n => chineseNumbers[parseInt(n)]).join('');
    }

    // 確定恢復數值和顏色
    let recoveryText = '';
    let recoveryColor = '';
    let recoveryfontfamily = '圓體';

    if (item.effect && item.effect.curHP) {
        recoveryText = `${numberToChinese(item.effect.curHP)}`;
        recoveryColor = '#87CEEB'; // 藍色表示HP恢復
    } else if (item.effect && item.effect.curMP) {
        recoveryText = `${numberToChinese(item.effect.curMP)}`;
        recoveryColor = '#FFD700'; // 金色表示MP恢復
    }

    if (!recoveryText) return; // 如果沒有恢復效果，直接返回

    // 計算玩家在canvas中的位置
    const playerX = player.Position % controlLayer[currentLevel].size.cols;
    const playerY = Math.floor(player.Position / controlLayer[currentLevel].size.cols);

    // 創建恢復數值物件，添加到mapObjects中
    const recoveryObj = {
        type: 'recoveryNumber',
        gridX: playerX,
        gridY: playerY,
        text: recoveryText,
        color: recoveryColor,
        fontFamily: recoveryfontfamily, // 使用圓體字體
        fontSize: 28,
        zIndex: 9999, // 確保在最上層
        // 動畫相關屬性
        startTime: performance.now(),
        duration: 3000, // 3秒動畫，與 setplayerdamagetext 一致
        // 字符彈跳動畫參數
        charAnimationDuration: 200,
        charDelay: 120,
        bounceHeight: 12,
        // 動畫階段參數
        fadeStartProgress: 0.7,
        isRecoveryAnimation: true
    };

    // 添加到mapObjects
    mapObjects.push(recoveryObj);

    // 3.5秒後移除恢復數值物件（動畫3秒 + 0.5秒緩衝）
    setTimeout(() => {
        const index = mapObjects.findIndex(obj => obj === recoveryObj);
        if (index !== -1) {
            mapObjects.splice(index, 1);
            render();
        }
    }, 3500);

    // 啟動動畫循環並立即渲染以顯示恢復數值
    if (typeof startAnimationLoop === 'function') {
        startAnimationLoop();
    }
    render();
}

// 創建背包操作選單:轉移、使用
function CreatePlayerOperationWindow(player) {
    console.log(`創建玩家 ${player.name} 的背包操作選單`);

    // 移除現有的背包選單
    const existingBagOptions = document.getElementById('optionsbag');
    if (existingBagOptions) {
        existingBagOptions.remove();
    }

    // 計算玩家位置
    const playerCol = player.Position % controlLayer[currentLevel].size.cols;
    const playerRow = Math.floor(player.Position / controlLayer[currentLevel].size.cols);

    // 獲取canvas的x,y座標
    const canvasX = playerCol * cellWidth;
    const canvasY = playerRow * cellHeight;

    // 計算選單位置（考慮相機偏移）
    const menuX = canvasX - cameraX + cellWidth / 2;
    const menuY = canvasY - cameraY + cellHeight / 2;

    // 創建背包操作選單
    const options = document.createElement('div');
    options.id = 'optionsbag';
    options.style.cssText = `
        position: absolute;
        left: ${menuX}px;
        top: ${menuY - 80}px;
        z-index: 1000;
        display: flex;
        flex-direction: row;
        gap: 10px;
        transform: translate(-50%, -50%);
    `;

    // 創建轉移按鈕
    const transferBtn = document.createElement('div');
    transferBtn.className = 'menu';
    transferBtn.id = 'transferbtn';


    // 創建使用物品按鈕
    const useItemBtn = document.createElement('div');
    useItemBtn.className = 'menu';
    useItemBtn.id = 'useitembtn';


    options.appendChild(transferBtn);
    options.appendChild(useItemBtn);

    // 添加到GameMap
    const targetElement = document.getElementById('GameMap');
    if (!targetElement) {
        console.error("找不到GameMap元素");
        return;
    }
    targetElement.appendChild(options);

    // 設置事件處理器
    transferBtn.onclick = function () {
        console.log(`玩家 ${player.name} 點擊轉移按鈕`);

        transferitem(player);
    };

    useItemBtn.onclick = function () {
        console.log(`玩家 ${player.name} 點擊使用物品按鈕`);

        useitem(player);
    };

    console.log(`背包操作選單已創建在位置 (${menuX}, ${menuY})`);
}

//使用物品按鈕
function useitem(player) {
    let playerbag = player.Inventory.filter(item => item.type === "medicine");
    //創建物品視窗
    runOBJ["當前物品操作"] = "使用";
    createitemwindow(player, playerbag, runOBJ["當前物品操作"]);
}

// 轉移物品
function transferitem(player) {
    // 不再過濾物品類型，使用所有物品
    let playerbag = player.Inventory;

    //創建物品視窗
    runOBJ["當前物品操作"] = "轉移";
    createitemwindow(player, playerbag, runOBJ["當前物品操作"]);
}

//創建物品視窗
function createitemwindow(player, playerbag, operatesitem) {
    Dom.InfoDialog.style.zIndex = 99;

    //抓取medicine類型的物品
    let eattingitem;

    //測試用:若operatesitem為"使用"，則只顯示"medicine"類型的物品，否則顯示所有物品
    if (operatesitem === "使用") {
        eattingitem = playerbag.filter(item => item.type === "medicine");
    } else {
        eattingitem = player.Inventory
    }

    const itemwindow = `
        <dialog id="itemdialog">
            <div id="itemwindow">
                    <div id="itemtoparea">
                        <div id="itemtitle">${runOBJ["當前物品操作"]}背包物品</div>
                        <div id="itemclosebtn"></div>
                    </div>
                   <div id="itemconatainer">
                   <div id="itemlist">
                    ${eattingitem.map((item, index) => `
                        <div id="item${index}" class="item">
                            <img src="${item.image}" class="itemimg">
                            <div class="itemname">${item.name}</div>
                        </div>
                    `).join('')}
                   </div> 
            </div>
        </dialog>
        `;

    setTimeout(() => {
        //滑鼠移入物品時顯示物品資訊使用title
        eattingitem.forEach((item, index) => {
            document.getElementById(`item${index}`).title = item.description;
        });

        //點擊物品時先記錄玩家點擊的物品
        eattingitem.forEach((item, index) => {
            document.getElementById(`item${index}`).onclick = function () {
                runOBJ["當前選取物品"] = item;

                // 清除背包選單（如果存在）
                const existingBagOptions = document.getElementById('optionsbag');
                if (existingBagOptions) {
                    existingBagOptions.remove();
                }

                clearAllHighlights();

                //清除物品視窗
                document.getElementById("itemdialog").close();
                document.getElementById("InfoDialog").innerHTML = "";
                Dom.InfoDialog.style.zIndex = -1;

                // 找到當前使用物品的玩家索引
                let playerIndex = gameplayers.findIndex(p => p.Position === player.Position);

                console.log(`玩家 ${player.name} 選擇了物品: ${item.name}, 操作: ${operatesitem}`);

                if (operatesitem === "轉移") {
                    bfsforuseitem(player.Position, 1, playerIndex, item, item.range, runOBJ["當前物品操作"]);
                } else if (operatesitem === "使用") {
                    bfsforuseitem(player.Position, 1, playerIndex, item, item.range, runOBJ["當前物品操作"]);
                }
            }
        });
    });

    document.getElementById("InfoDialog").innerHTML = itemwindow;
    document.getElementById("itemdialog").showModal();
    document.getElementById("itemclosebtn").onclick = function () {
        document.getElementById("itemdialog").close();
        document.getElementById("InfoDialog").innerHTML = "";
        Dom.InfoDialog.style.zIndex = -1;
    }
}

//使用物品的bfs (修正為canvas系統)
function bfsforuseitem(startPosition, moveRange, playerIndex, selectedItem, itemrange, operatesitem) {
    console.log(`開始物品使用範圍計算: 玩家${playerIndex}, 物品: ${selectedItem.name}, 操作: ${operatesitem}`);

    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    // 清除之前的高亮
    clearAllHighlights();

    // 存儲可使用物品的位置
    const usablePositions = [];

    // 如果是使用物品，玩家自己的位置也可以使用
    if (operatesitem === "使用") {
        usablePositions.push(startPosition);
        const startX = startPosition % controlLayer[currentLevel].size.cols;
        const startY = Math.floor(startPosition / controlLayer[currentLevel].size.cols);
        highlightCells.push({
            x: startX,
            y: startY,
            color: 'rgba(245, 0, 216, 0.78)' // 綠色表示可使用
        });
    }

    const queue = [{ position: startPosition, distance: 0 }];
    const visited = new Set();
    visited.add(startPosition);

    while (queue.length > 0) {
        const { position, distance } = queue.shift();

        if (distance < moveRange) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                // Check boundaries
                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    if (!visited.has(newPosition) &&
                        !gameobstacles.includes(newPosition) &&
                        !gameenemys.some(enemy => enemy.Position === newPosition && enemy.CurHP > 0)) {

                        visited.add(newPosition);
                        queue.push({ position: newPosition, distance: distance + 1 });

                        // 添加到可使用位置列表
                        usablePositions.push(newPosition);

                        // 使用canvas高亮系統
                        highlightCells.push({
                            x: newX,
                            y: newY,
                            color: operatesitem === "轉移" ? 'rgba(245, 0, 216, 0.78)' : 'rgba(245, 0, 216, 0.78)'
                        });
                    }
                }
            });
        }
    }

    // 渲染高亮
    render();

    // 設置canvas點擊事件處理
    setupItemUseClickHandler(usablePositions, playerIndex, selectedItem, operatesitem);

    console.log(`物品使用範圍計算完成，共 ${usablePositions.length} 個可用位置`);
}

// 設置物品使用的canvas點擊事件處理
function setupItemUseClickHandler(usablePositions, playerIndex, selectedItem, operatesitem) {
    // 移除之前的點擊事件監聽器
    if (window.currentItemUseClickHandler) {
        canvas.removeEventListener('click', window.currentItemUseClickHandler);
        window.currentItemUseClickHandler = null;
    }

    // 移除之前的滑鼠移動事件監聽器
    if (window.currentItemUseMouseMoveHandler) {
        canvas.removeEventListener('mousemove', window.currentItemUseMouseMoveHandler);
        window.currentItemUseMouseMoveHandler = null;
    }

    // 存儲當前高亮的物品作用範圍
    let currentItemRangeHighlights = [];

    // 創建滑鼠移動處理器 - 用於顯示物品作用範圍
    const itemUseMouseMoveHandler = async function (event) {
        // 獲取滑鼠位置
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX - rect.left + cameraX;
        const y = event.clientY - rect.top + cameraY;

        // 計算滑鼠所在的格子位置
        const hoveredCol = Math.floor(x / cellWidth);
        const hoveredRow = Math.floor(y / cellHeight);
        const hoveredPosition = hoveredRow * controlLayer[currentLevel].size.cols + hoveredCol;

        // 檢查是否在可使用範圍內
        if (usablePositions.includes(hoveredPosition)) {
            // 清除之前的物品作用範圍高亮
            clearAllItemRangeHighlights();
            currentItemRangeHighlights = [];

            // 顯示物品作用範圍
            try {
                currentItemRangeHighlights = await showItemEffectRange(hoveredPosition, selectedItem, operatesitem);
            } catch (error) {
                console.error('顯示物品作用範圍失敗:', error);
                currentItemRangeHighlights = [];
            }
        } else {
            // 如果不在可使用範圍內，清除物品作用範圍高亮
            clearAllItemRangeHighlights();
            currentItemRangeHighlights = [];
        }
    };

    // 創建新的點擊處理器
    const itemUseClickHandler = function (event) {
        // 獲取點擊位置
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX - rect.left + cameraX;
        const y = event.clientY - rect.top + cameraY;

        // 計算點擊的格子位置
        const clickedCol = Math.floor(x / cellWidth);
        const clickedRow = Math.floor(y / cellHeight);
        const clickedPosition = clickedRow * controlLayer[currentLevel].size.cols + clickedCol;

        console.log(`點擊位置: (${clickedRow}, ${clickedCol}) = ${clickedPosition}`);

        // 檢查是否點擊了可使用的位置
        if (usablePositions.includes(clickedPosition)) {
            console.log(`在可使用位置 ${clickedPosition} 執行 ${operatesitem} 操作`);

            // 移除事件監聽器
            canvas.removeEventListener('click', itemUseClickHandler);
            canvas.removeEventListener('mousemove', itemUseMouseMoveHandler);
            window.currentItemUseClickHandler = null;
            window.currentItemUseMouseMoveHandler = null;

            // 清除物品作用範圍高亮
            clearAllItemRangeHighlights();

            // 執行物品使用邏輯
            executeItemUse(clickedPosition, playerIndex, selectedItem, operatesitem);
        } else {
            console.log("點擊位置不在可使用範圍內");
        }
    };

    // 添加事件監聽器
    canvas.addEventListener('click', itemUseClickHandler);
    canvas.addEventListener('mousemove', itemUseMouseMoveHandler);
    window.currentItemUseClickHandler = itemUseClickHandler;
    window.currentItemUseMouseMoveHandler = itemUseMouseMoveHandler;

    console.log("物品使用點擊和滑鼠移動事件監聽器已設置");
}

// 顯示物品作用範圍
async function showItemEffectRange(centerPosition, selectedItem, operatesitem) {
    const itemRangeHighlights = [];

    // 確定使用的範圍目錄和尺寸
    const rangeDir = selectedItem.range || 0;
    const centerX = centerPosition % controlLayer[currentLevel].size.cols;
    const centerY = Math.floor(centerPosition / controlLayer[currentLevel].size.cols);

    // 根據range確定圖片尺寸
    let imageWidth, imageHeight;
    switch (rangeDir) {
        case 0:
            imageWidth = 110;
            imageHeight = 88;
            break;
        case 1:
            imageWidth = 330;
            imageHeight = 264;
            break;
        default:
            // 可以根據需要添加更多range的尺寸
            imageWidth = 110 + (rangeDir * 110); // 預設按比例增加
            imageHeight = 88 + (rangeDir * 88);
            break;
    }

    // 載入範圍圖片並創建單一高亮物件
    try {
        const rangeImages = await loadRangeImages(rangeDir);
        itemRangeHighlights.push({
            x: centerX,
            y: centerY,
            type: 'rangeImage',
            images: rangeImages,
            currentFrame: 0,
            lastFrameTime: 0,
            frameInterval: 100, // 每100ms切換一幀
            imageWidth: imageWidth,
            imageHeight: imageHeight
        });
    } catch (error) {
        console.error('載入範圍圖片失敗，跳過顯示:', error);
        // 如果圖片載入失敗，就不顯示任何高亮，避免混淆
        return [];
    }

    // 添加到highlightCells並渲染
    highlightCells.push(...itemRangeHighlights);
    render();

    // 如果有圖片動畫，啟動動畫循環
    const hasImageHighlights = itemRangeHighlights.some(highlight => highlight.type === 'rangeImage');
    if (hasImageHighlights && typeof startAnimationLoop === 'function') {
        startAnimationLoop();
    }

    return itemRangeHighlights;
}

// 載入範圍圖片陣列
async function loadRangeImages(rangeDir) {
    const images = [];
    const maxImages = 36; // 根據目錄中的圖片數量調整

    for (let i = 0; i < maxImages; i++) {
        try {
            const imagePath = `./Public/Cursor/${rangeDir}/${i}.png`;
            const img = await preloadImage(imagePath);
            images.push(img);
        } catch (error) {
            // 如果載入失敗，停止載入更多圖片
            console.log(`載入範圍圖片 ${i} 失敗，共載入 ${images.length} 張圖片`);
            break;
        }
    }

    if (images.length === 0) {
        throw new Error(`無法載入範圍 ${rangeDir} 的圖片`);
    }

    console.log(`成功載入範圍 ${rangeDir} 的 ${images.length} 張圖片`);
    return images;
}

// 清除物品作用範圍高亮
function clearItemRangeHighlights(itemRangeHighlights) {
    if (!itemRangeHighlights || itemRangeHighlights.length === 0) {
        return;
    }

    // 從highlightCells中移除物品作用範圍高亮
    itemRangeHighlights.forEach(highlight => {
        const index = highlightCells.findIndex(cell =>
            cell.x === highlight.x &&
            cell.y === highlight.y &&
            (cell.color === highlight.color ||
                cell.type === highlight.type ||
                cell.type === 'rangeImage')
        );
        if (index !== -1) {
            highlightCells.splice(index, 1);
        }
    });

    // 重新渲染
    render();
}

// 清除所有物品作用範圍高亮（更徹底的清除方法）
function clearAllItemRangeHighlights() {
    // 移除所有 rangeImage 類型的高亮
    for (let i = highlightCells.length - 1; i >= 0; i--) {
        if (highlightCells[i].type === 'rangeImage') {
            highlightCells.splice(i, 1);
        }
    }

    // 重新渲染
    render();
}

// 執行物品使用邏輯
async function executeItemUse(targetPosition, playerIndex, selectedItem, operatesitem) {
    console.log(`執行物品使用: 位置${targetPosition}, 玩家${playerIndex}, 物品${selectedItem.name}, 操作${operatesitem}`);

    // 清除所有事件監聽器
    if (window.currentItemUseMouseMoveHandler) {
        canvas.removeEventListener('mousemove', window.currentItemUseMouseMoveHandler);
        window.currentItemUseMouseMoveHandler = null;
    }

    // 清除高亮
    clearAllHighlights();

    const currentPlayer = gameplayers[playerIndex];

    if (operatesitem === "轉移") {
        // 檢查目標位置是否有玩家
        const targetPlayer = gameplayers.find(p => p.Position === targetPosition);
        if (targetPlayer) {
            console.log(`轉移物品給玩家: ${targetPlayer.name}`);

            // 轉移物品給目標玩家
            const transferItem = runOBJ["當前選取物品"];

            if (transferItem) {
                // 從當前玩家背包中移除物品
                const itemIndex = currentPlayer.Inventory.findIndex(i => i === transferItem);

                if (itemIndex !== -1) {
                    currentPlayer.Inventory.splice(itemIndex, 1);

                    // 添加物品到目標玩家背包
                    targetPlayer.Inventory.push(transferItem);

                    console.log(`物品 ${transferItem.name} 已從 ${currentPlayer.name} 轉移給 ${targetPlayer.name}`);

                    // 顯示轉移消息
                    showitemtransfermsg(transferItem, currentPlayer, targetPlayer);
                } else {
                    console.error("在當前玩家背包中找不到要轉移的物品");
                }
            } else {
                console.error("沒有選中要轉移的物品");
            }
        } else {
            console.log("目標位置沒有玩家，無法轉移物品");
        }
    } else if (operatesitem === "使用") {
        // 使用物品邏輯
        console.log(`使用物品: ${selectedItem.name}`);

        const useItem = runOBJ["當前選取物品"];
        if (useItem && useItem.type === "medicine") {
            // 計算物品作用範圍內的所有玩家
            const affectedPlayers = getPlayersInItemRange(targetPosition, useItem);

            if (affectedPlayers.length > 0) {
                // 從背包中移除物品
                const itemIndex = currentPlayer.Inventory.findIndex(i => i === useItem);
                if (itemIndex !== -1) {
                    currentPlayer.Inventory.splice(itemIndex, 1);

                    // 對所有受影響的玩家應用物品效果
                    affectedPlayers.forEach(targetPlayer => {
                        if (useItem.effect) {
                            if (useItem.effect.curHP) {
                                targetPlayer.CurHP = Math.min(targetPlayer.HP, targetPlayer.CurHP + useItem.effect.curHP);
                                console.log(`${targetPlayer.name} 恢復了 ${useItem.effect.curHP} HP`);
                            }
                            if (useItem.effect.curMP) {
                                targetPlayer.curMp = Math.min(targetPlayer.MP, targetPlayer.curMp + useItem.effect.curMP);
                                console.log(`${targetPlayer.name} 恢復了 ${useItem.effect.curMP} MP`);
                            }
                        }
                        console.log(`${currentPlayer.name} 對 ${targetPlayer.name} 使用了 ${useItem.name}`);
                    });

                    // 播放物品使用動畫（傳遞所有受影響的玩家）
                    await animationforeatting(affectedPlayers, useItem);
                } else {
                    console.error("在背包中找不到要使用的物品");
                }
            } else {
                console.log("目標範圍內沒有玩家，無法使用物品");
            }
        } else {
            console.error("選中的物品不是藥品類型或物品不存在");
        }
    }

    // 完成物品使用後的清理工作
    await completeItemUse(currentPlayer, playerIndex);
}

// 完成物品使用後的清理工作
async function completeItemUse(player, playerIndex) {
    console.log(`完成玩家 ${player.name} 物品使用操作`);

    // 處理蓄力狀態
    if (player["是否蓄力"] === true && player["是否釋放蓄力"] === false) {
        player.Move -= 2;
        player["是否蓄力"] = false;
        player["是否釋放蓄力"] = true;
        console.log(`玩家 ${player.name} 釋放蓄力，移動力減少2`);
    }

    // 標記玩家已完成行動
    player.AlreadyMove = true;
    player.OldPosition = player.Position;

    // 重置物品操作狀態
    runOBJ["當前選取"] = null;
    runOBJ["第二次選取"] = null;
    runOBJ["當前物品操作"] = null;
    runOBJ["當前選取物品"] = null;

    // 清除高亮顯示
    clearAllHighlights();

    // 檢查是否有寶箱（如果玩家在寶箱位置）
    for (const [index, treasure] of gametreasures.entries()) {
        if (treasure["位置"] === player.Position) {
            player.Inventory.push(treasure["寶物"]);
            console.log(`玩家 ${player.name} 拾取了寶物: ${treasure["寶物"].name}`);

            // 追蹤物品獲得（用於成就系統）
            if (!window.achievementTracker) {
                window.achievementTracker = {
                    magicAttackCount: 0,
                    itemsCollected: new Set()
                };
            }
            window.achievementTracker.itemsCollected.add(treasure["寶物"].name);
            console.log(`物品獲得追蹤: ${treasure["寶物"].name}`);

            // 更新成就進度條（如果設定視窗開啟）
            if (typeof Game !== 'undefined' && Game.updateAchievementProgress) {
                Game.updateAchievementProgress();
            }

            await wait(2);
            await showyouget(player, treasure["寶物"]);

            // 記錄寶箱已被拿取
            if (typeof window.takenTreasures === 'undefined') {
                window.takenTreasures = new Set();
            }
            window.takenTreasures.add(treasure["位置"]);
            console.log(`記錄寶箱已拿取: 位置 ${treasure["位置"]}`);

            gametreasures.splice(index, 1);
            break;
        }
    }

    // 更新canvas顯示
    updateMapObjects();
    render();

    // 重要：異步延遲使重複選取角色不會觸發兩次選單
    setTimeout(() => {
        runOBJ["當前操作"] = null;
    }, 100);

    console.log(`玩家 ${player.name} 物品使用操作完成`);

    await wait(1);
    if (allPlayersMoved()) {
        runOBJ["當前行動方"] = "Enemys";
        await wait(0.5);
        EnemysAction();
        return;
    }
}

//顯示物品轉移動畫控制
function showitemtransfermsg(item, currentPlayer, targetPlayer) {
    const transferMsg = `
                        <dialog id="transferdialog">
                            <div id="transferMsg">
                                <p>${currentPlayer.name} 將 【${item.name}】 轉移給 ${targetPlayer.name}</p>
                            </div>
                        </dialog>
                        `;

    const transferMsgElement = document.createElement("div");
    transferMsgElement.innerHTML = transferMsg;
    Dom.InfoDialog.appendChild(transferMsgElement);

    setTimeout(() => {
        document.getElementById("transferdialog").showModal();
    })


    setTimeout(() => {
        document.getElementById("transferdialog").close();
        Dom.InfoDialog.removeChild(transferMsgElement);

    }, 2000)

}

