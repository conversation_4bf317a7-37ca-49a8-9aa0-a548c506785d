#sidebar {
    width: 5vw;
    height: 100vh;
    background: rgb(247, 231, 173);
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;
    justify-self: flex-start;
    gap: 10px;
    overflow: hidden;
}

.option {
    width: 100%;
    height: 12%;
    font-size: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: brown;
    color: rgb(168, 105, 38);
    font-weight: 600;
    letter-spacing: 2px;
    border: 4px solid rgb(165, 90, 24);
    text-wrap: wrap;
    cursor: pointer;
    transition: all 0.3s;
    gap: 5px;
}

.option:hover {
    color: #eee;
    background-color: rgb(165, 90, 24);
    border: 4px solid rgb(168, 105, 38);
}

.option img {
    width: 50%;
}

#GPSplayer {
    position: absolute;
    bottom: 5%;
    right: 8%;
    font-size: 30px;
    font-weight: 600;
    border-radius: 50%;
    width: 70px;
    height: 70px;
    background: rgb(247, 231, 173);
    border: 4px solid rgb(165, 90, 24);
    transition: .5s;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    text-align: center;
}

#GPSplayer:hover {
    transform: scale(1.1);
    transform-origin: center;
    box-shadow: 0 0 5px black;
}