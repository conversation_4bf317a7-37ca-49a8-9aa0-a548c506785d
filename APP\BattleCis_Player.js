// 玩家攻擊:暴擊總共占用秒數延遲為 0.8秒
async function playerAttack(player, enemy, enemyIndex, playerCanvas, enemyCanvas) {
    let criticalhitrate = player.CriticalHitRate + Math.floor(Math.random() * (100 - player.CriticalHitRate));
    // 計算裝備加成後的攻擊力
    let totalATK = player.ATK;
    if (player.Equipment.Weapon) {
        totalATK += player.Equipment.Weapon.ATK;
    }
    if (player.Equipment.Fitting && player.Equipment.Fitting.effect?.ATK) {
        totalATK += player.Equipment.Fitting.effect.ATK;
    }

    if (criticalhitrate >= 100) {
        let atktwotimes = Math.random() > 0.5; // 隨機決定是否連續攻擊兩次
        if (atktwotimes) {
            let playerdamage = Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 0.8 - enemy.DEF) < 0
                ? 1
                : Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 0.8 - enemy.DEF);

            let playerimg = document.createElement("img");
            let enemyimg = document.createElement("img");

            // ===========================等待0秒，目前時間0秒=============================
            await wait(0);
            setplayercriticalimg(playerCanvas)                  // 設置玩家暴擊特效圖片
            drawPlayerIdleAnimation(player, playerCanvas, enemyCanvas)     // 設置玩家站姿圖片
            drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)       // 設置敵人站姿圖片

            // ===========================等待0.6秒，目前時間0.6秒========================
            await wait(0.6);
            player.function();  // 攻擊畫面震動特效
            // ===========================等待0.2秒，目前時間0.8秒========================
            await wait(0.2);
            drawPlayerATKAnimation(player, playerCanvas, enemyCanvas);  // 設置玩家攻擊圖片
            await player.bloodfun(playerdamage, enemy);    //血條動畫特效，最後回傳是否敵人死亡
            // ===========================等待0.25秒，目前時間1秒========================
            await wait(0.5); // 等待0.25秒
            player.function();  // 攻擊畫面震動特效

            await wait(0.2); // 等待0.25秒
            drawPlayerATKAnimation(player, playerCanvas, enemyCanvas);  // 設置玩家攻擊圖片
            const isDead = await player.bloodfun(playerdamage, enemy);    //血條動畫特效，最後回傳是否敵人死亡
            // ===========================等待0.25秒，目前時間1.25秒========================
            await wait(0.25); // 等待0.25秒
            setplayerdamagetext(playerdamage * 2); // 設置玩家傷害數字顯示
            return { isDead };
        } else {
            let playerdamage = Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 1.8 - enemy.DEF) < 0
                ? 1
                : Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 1.8 - enemy.DEF);

            // ===========================等待0秒，目前時間0秒=============================
            await wait(0);
            setplayercriticalimg(playerCanvas)                  // 設置玩家暴擊特效圖片
            drawPlayerIdleAnimation(player, playerCanvas, enemyCanvas)     // 設置玩家站姿圖片
            drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)       // 設置敵人站姿圖片

            // ===========================等待0.6秒，目前時間0.6秒========================
            await wait(0.6);
            player.function();  // 攻擊畫面震動特效

            // ===========================等待0.2秒，目前時間0.8秒========================
            await wait(0.2);
            drawPlayerATKAnimation(player, playerCanvas, enemyCanvas);  // 設置玩家攻擊圖片
            const isDead = await player.bloodfun(playerdamage, enemy);    //血條動畫特效，最後回傳是否敵人死亡

            // ===========================等待0.25秒，目前時間1秒========================
            await wait(0.25); // 等待0.2秒
            setplayerdamagetext(playerdamage); // 設置玩家傷害數字顯示

            return { isDead };
        }

    } else {
        let playerdamage = Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 0.98 - enemy.DEF) < 0
            ? 1
            : Math.floor(Math.random() * (20) + totalATK * 0.98 - enemy.DEF);

        // ===========================等待0秒，目前時間0秒=============================
        await wait(0)
        player.function();  // 攻擊畫面震動特效
        // 使用封裝後的動畫繪製 function
        drawPlayerATKAnimation(player, playerCanvas, enemyCanvas);   // 設置玩家攻擊圖片
        drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas);    // 設置敵人受傷圖片

        // ===========================等待0.6秒，目前時間0.6秒========================
        await wait(0.1);
        const isDead = await player.bloodfun(playerdamage, enemy);    //血條動畫特效，最後回傳是否敵人死亡

        // ===========================等待0.25秒，目前時間0.85秒========================
        await wait(0.25); // 等待0.25秒
        setplayerdamagetext(playerdamage); // 設置玩家傷害數字顯示

        return { isDead };
    }
}

//玩家攻擊(未命中)
async function playerAttackMiss(player, enemy, playerCanvas, enemyCanvas) {

    // ===========================等待0秒，目前時間0秒=============================
    await wait(0);
    drawPlayerATKMissAnimation(player, playerCanvas, enemyCanvas)
    drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)

    player.function();

    // ===========================等待0.6秒，目前時間0.6秒========================
    await wait(0.6);
    setplayerdamagetext(0); // 設置玩家傷害數字顯示
}

//敵人反擊
async function enemyAttack(player, enemy, playerCanvas, enemyCanvas) {
    let enemycriticalhitrate = enemy.CriticalHitRate + Math.floor(Math.random() * (50 - enemy.CriticalHitRate / 2));

    // 計算裝備加成後的防禦力
    let totalDEF = player.DEF;
    if (player.Equipment.Armor) {
        totalDEF += player.Equipment.Armor.DEF;
    }
    if (player.Equipment.Fitting && player.Equipment.Fitting.effect?.DEF) {
        totalDEF += player.Equipment.Fitting.effect.DEF;
    }
    // 計算敵人攻擊力
    if (enemycriticalhitrate >= 100) {
        let atktwotimes = Math.random() > 0.5; // 隨機決定是否連續攻擊兩次
        if (atktwotimes) {
            let enemydamage = Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK * 0.8 - totalDEF) < 0
                ? 1
                : Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK * 0.8 - totalDEF);
            await wait(4);
            setenemycriticalimg(enemyCanvas)

            await wait(0.8);
            drawEnemyAttackAnimation(enemy, enemyCanvas, playerCanvas)
            await enemy.bloodfun(enemydamage, player);

            await wait(1.5);
            drawEnemyAttackAnimation(enemy, enemyCanvas, playerCanvas)
            const playerisdied = await enemy.bloodfun(enemydamage, player);

            await wait(1.5);
            setenemydamagetext(enemydamage * 2);

            await wait(1);
            return { playerisdied };
        } else {
            let enemydamage = Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK * 1.8 - totalDEF) < 0
                ? 1
                : Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK * 1.8 - totalDEF);
            await wait(4);
            setenemycriticalimg(enemyCanvas)

            await wait(0.8);
            drawEnemyAttackAnimation(enemy, enemyCanvas, playerCanvas)
            const playerisdied = await enemy.bloodfun(enemydamage, player);

            await wait(1.5);
            setenemydamagetext(enemydamage);

            await wait(1);
            return { playerisdied };
        }
    } else {
        let enemydamage = Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK - totalDEF) < 0
            ? 1
            : Math.floor(Math.random() * (20) + enemy.ATK - totalDEF);
        await wait(4.8);

        drawEnemyAttackAnimation(enemy, enemyCanvas, playerCanvas);
        const playerisdied = await enemy.bloodfun(enemydamage, player);

        await wait(2);
        setenemydamagetext(enemydamage);

        return { playerisdied };
    }
}

//敵人反擊(未命中)
async function enemyAttackMiss(player, enemy, playerCanvas, enemyCanvas) {

    await wait(3);
    drawEnemyAttackMissAnimation(enemy, enemyCanvas, playerCanvas)
    await wait(0.65);
    setenemydamagetext(0); // 設置敵人傷害數字顯示
}

//判斷敵人與玩家的距離是否在敵人的攻擊範圍內
function isPlayerInRange(player, enemy) {
    let playerX = player.Position % Level0.size.cols;
    let playerY = Math.floor(player.Position / Level0.size.cols);
    let enemyX = enemy.Position % Level0.size.cols;
    let enemyY = Math.floor(enemy.Position / Level0.size.cols);

    let distance = Math.abs(playerX - enemyX) + Math.abs(playerY - enemyY);

    return distance <= enemy.ATKRange;
}

//判斷敵人是否在玩家的攻擊範圍內
function isEnemyInRange(player, enemy) {
    let playerX = player.Position % Level0.size.cols;
    let playerY = Math.floor(player.Position / Level0.size.cols);
    let enemyX = enemy.Position % Level0.size.cols;
    let enemyY = Math.floor(enemy.Position / Level0.size.cols);

    let distance = Math.abs(playerX - enemyX) + Math.abs(playerY - enemyY);

    return distance <= player.ATKRange;
}


