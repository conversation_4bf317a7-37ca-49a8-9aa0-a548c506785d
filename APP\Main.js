//查看角色資訊功能
function lookrole(player) {
    // 檢查是否在營地場景
    if (typeof sceneManager !== 'undefined' && sceneManager.sceneType === 'camp') {
        console.log("在營地場景中查看角色資訊");
        // 在營地場景中允許查看角色資訊
    } else {
        // 在戰鬥場景中的檢查
        if (typeof runOBJ !== 'undefined') {
            if (runOBJ["當前行動方"] === "Enemys") {
                console.log("敵人回合中，無法查看角色");
                return;
            }
            // 允許在以下狀態查看角色：
            // - null (無操作)
            // - "選擇移動格子中" (顯示移動範圍時)
            // 禁止在以下狀態查看角色：
            // - "正在移動中", "已經移動但未進行操作", "攻擊中", "選擇攻擊目標中"
            const blockedStates = ["正在移動中", "已經移動但未進行操作", "攻擊中", "選擇攻擊目標中"];
            if (blockedStates.includes(runOBJ["當前操作"])) {
                console.log(`當前操作狀態 "${runOBJ["當前操作"]}" 不允許查看角色`);
                return;
            }
        }
    }

    // 設置當前查看的玩家
    currentViewedPlayer = player;

    Dom.InfoDialog.style.zIndex = 99;

    let playerRole = player["圖片"];
    let playerHP = player.HP;
    let playercurHp = player.CurHP;
    let playerMP = player.MP;
    let playercurMp = player.CurMP;
    let playerATK = player.ATK;
    let playerDEF = player.DEF;
    let playerMove = player.Move;
    let playerEXP = player.EXP;
    let playercurexp = player.CurEXP;
    let playerLevel = player.level;

    
    let playerInfo = `<dialog id="ifnodialog_inner"></dialog>`;
    //在dialog中添加canvas並使用canvas繪製
    let canvas = document.createElement('canvas');
    canvas.width = 1200;
    canvas.height = 650;
    canvas.style.cssText = `position: absolute; left: 30%; transform: translateX(-30%); z-index: 11;`;
    document.getElementById("InfoDialog").appendChild(canvas);
    let ctx = canvas.getContext('2d');

    // 在canvas上繪製視窗圖片
    let windowimg = new Image();
    windowimg.src = "./Public/Roleinfo/leftwindow.png";
    windowimg.onload = function () {
        ctx.drawImage(windowimg, 0, 0, 550, 600);
    };

    document.getElementById("InfoDialog").innerHTML = playerInfo;
    document.getElementById("ifnodialog_inner").showModal();
    document.getElementById("closeinfobtn").onclick = function () {
        document.getElementById("ifnodialog_inner").close();
        document.getElementById("InfoDialog").innerHTML = "";
        Dom.InfoDialog.style.zIndex = -1;
    }
}

//結束回合按鈕
Dom.EndTurnBtn.onclick = () => {
    // 檢查是否在營地場景
    if (typeof sceneManager !== 'undefined' && sceneManager.sceneType === 'camp') {
        console.log("在營地場景中，結束回合按鈕無效");
        return;
    }

    // 在戰鬥場景中的檢查
    if (typeof runOBJ !== 'undefined') {
        if (runOBJ["當前行動方"] === "Enemys") {
            console.log("敵人回合中，無法結束回合");
            return;
        }
        // 允許在以下狀態結束回合：
        // - null (無操作)
        // - "選擇移動格子中" (顯示移動範圍時)
        // 禁止在以下狀態結束回合：
        // - "正在移動中", "已經移動但未進行操作", "攻擊中", "選擇攻擊目標中"
        const blockedStates = ["正在移動中", "已經移動但未進行操作", "攻擊中", "選擇攻擊目標中"];
        if (blockedStates.includes(runOBJ["當前操作"])) {
            console.log(`當前操作狀態 "${runOBJ["當前操作"]}" 不允許結束回合`);
            return;
        }
    } else {
        console.log("runOBJ 未定義，無法結束回合");
        return;
    }


    let questionwindow = document.createElement("dialog");
    questionwindow.id = "questiondialog";
    questionwindow.innerHTML = `
        <div id="questiondialog_inner">
            <div id="question">確定要結束回合嗎？</div>
            <div id="questionbtn">
                <div id="yes">確定</div>
                <div id="no">取消</div>
            </div>
        </div>
    `;
    Dom.InfoDialog.appendChild(questionwindow);
    questionwindow.showModal();

    document.getElementById("yes").onclick = function () {
        questionwindow.close();
        Dom.InfoDialog.removeChild(questionwindow);
        runOBJ["當前行動方"] = "Enemys";
        resetRunOBJ();
        EnemysAction()
    }

    document.getElementById("no").onclick = function () {
        questionwindow.close();
        Dom.InfoDialog.removeChild(questionwindow);
    }

}

//存檔功能
Dom.SaveBtn.addEventListener('click', debounce(saveGameFunc, 100))

//讀檔功能
Dom.LoadBtn.addEventListener('click', debounce(loadGameFunc, 100))

function saveGameFunc() {
    // 檢查是否在營地場景
    if (typeof sceneManager !== 'undefined' && sceneManager.sceneType === 'camp') {
        console.log("在營地場景中執行存檔");
        // 在營地場景中允許存檔，使用場景管理器的存檔功能
        try {
            sceneManager.savePlayerData();
            sceneManager.saveGameProgress();
            alert("遊戲已保存！");
            return;
        } catch (error) {
            console.error("營地存檔失敗:", error);
            alert("存檔失敗！");
            return;
        }
    }

    // 在戰鬥場景中的檢查
    if (typeof runOBJ !== 'undefined') {
        if (runOBJ["當前行動方"] === "Enemys") {
            console.log("敵人回合中，無法存檔");
            return;
        }
        // 允許在以下狀態存檔：
        // - null (無操作)
        // - "選擇移動格子中" (顯示移動範圍時)
        // 禁止在以下狀態存檔：
        // - "正在移動中", "已經移動但未進行操作", "攻擊中", "選擇攻擊目標中"
        const blockedStates = ["正在移動中", "已經移動但未進行操作", "攻擊中", "選擇攻擊目標中"];
        if (blockedStates.includes(runOBJ["當前操作"])) {
            alert("目前操作中，如要存檔，請先按下取消動作");
            return;
        }
    } else {
        console.log("runOBJ 未定義，無法在戰鬥場景存檔");
        return;
    }
    // 創建 dialog 元素
    let saveDialog = document.createElement("dialog");
    saveDialog.id = "savegame-dialog";

    // 創建 dialog 內容容器
    let saveContent = document.createElement("div");
    saveContent.className = "save-content";

    // 添加標題
    let title = document.createElement("div");
    title.className = "save-title";
    title.innerHTML = Dom.InfoDialog.innerHTML || "存取進度";
    saveContent.appendChild(title);

    // 添加關閉按鈕
    let closeButton = document.createElement("button");
    closeButton.className = "save-close-btn";
    closeButton.textContent = "↩";
    closeButton.addEventListener("click", () => {
        saveDialog.close();
        Dom.GameMap.removeChild(saveDialog);
    });
    saveContent.appendChild(closeButton);

    // 創建存檔槽容器
    let slotsContainer = document.createElement("div");
    slotsContainer.className = "save-slots-container";

    // 創建五個存檔槽
    for (let i = 0; i < 5; i++) {
        let slot = document.createElement("div");
        slot.className = "save-slot";

        // 左欄：圖騰
        let totem = document.createElement("div");
        totem.className = "save-slot-totem";
        let totemImage = document.createElement("img");
        totemImage.src = gameplayers.length > 0 ? "./Public/saveicon.png" : "";
        totem.appendChild(totemImage);
        slot.appendChild(totem);

        // 中間欄：玩家等級和存檔時間
        let middle = document.createElement("div");
        middle.className = "save-slot-middle";
        let playerLevel = document.createElement("div");
        playerLevel.className = "save-slot-level";
        let saveTime = document.createElement("div");
        saveTime.className = "save-slot-time";
        let saveData = JSON.parse(localStorage.getItem(`saveSlot${i}`));
        if (saveData) {
            playerLevel.textContent = saveData.allPlayers
                ? `位階： ${saveData.allPlayers.player1.level}`
                : "位階： 無";
            saveTime.textContent = new Date(saveData.timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else {
            playerLevel.style.display = "none";
            saveTime.textContent = "";
        }
        middle.appendChild(playerLevel);
        middle.appendChild(saveTime);
        slot.appendChild(middle);

        // 右欄：關卡標題
        let title = document.createElement("div");
        title.className = "save-slot-title";
        title.textContent = saveData && saveData.currentLevel >= 0 && saveData.currentLevel < controlLayer.length
            ? controlLayer[saveData.currentLevel]["標題"] || `關卡 ${saveData.currentLevel}`
            : " ";
        slot.appendChild(title);

        // 存檔槽點擊事件
        slot.addEventListener("click", () => {
            let confirmSave = confirm(`是否要存檔在第 ${i + 1} 格的位置？`);
            if (confirmSave) {
                // 創建一個乾淨的玩家狀態副本
                const cleanPlayers = {};
                gameplayers.forEach(player => {
                    cleanPlayers[player.id] = {
                        id: player.id,
                        name: player.name,
                        level: player.level,
                        HP: player.HP,
                        CurHP: player.CurHP,
                        MP: player.MP,
                        CurMP: player.CurMP,
                        ATK: player.ATK,
                        DEF: player.DEF,
                        Move: player.Move,
                        EXP: player.EXP,
                        CurEXP: player.CurEXP,
                        Position: player.Position,
                        OldPosition: player.OldPosition,
                        atkrange: player.atkrange,
                        AvoidRate: player.AvoidRate,
                        HitRate: player.HitRate,
                        CriticalHitRate: player.CriticalHitRate,
                        "點數": player["點數"],
                        "是否蓄力": player["是否蓄力"],
                        "是否釋放蓄力": player["是否釋放蓄力"],
                        "已增加移動力": player["已增加移動力"],
                        "是否電腦操作": player["是否電腦操作"],
                        AlreadyMove: player.AlreadyMove,
                        Equipment: {
                            Weapon: player.Equipment.Weapon ? { ...player.Equipment.Weapon } : null,
                            Armor: player.Equipment.Armor ? { ...player.Equipment.Armor } : null,
                            Fitting: player.Equipment.Fitting ? { ...player.Equipment.Fitting } : null
                        },
                        Inventory: player.Inventory.map(item => ({ ...item })),
                        五內: { ...player.五內 },
                        法抗: { ...player.法抗 },
                        法術: player.法術.map(spell => ({
                            name: spell.name,
                            type: spell.type,
                            Classify: spell.Classify,
                            NeedMove: spell.NeedMove,
                            NeedLV: spell.NeedLV,
                            Range: spell.Range,
                            distance: spell.distance, // 保存法術的施行距離
                            Rmdistance: spell.Rmdistance, // 保存法術的最小距離
                            NeedMP: spell.NeedMP,
                            NeedSoul: { ...spell.NeedSoul },
                            Icon: spell.Icon,
                            Animations: spell.Animations,
                            Sounds: spell.Sounds
                        }))
                    };
                });

                // 保存敵人資料
                let enemyState = {};
                // 從 controlLayer 獲取初始敵人資料
                if (controlLayer[currentLevel] && controlLayer[currentLevel].Enemys) {
                    controlLayer[currentLevel].Enemys.forEach((initialEnemy, index) => {
                        // 查找當前敵人狀態
                        const currentEnemy = gameenemys.find(e => e.Position === initialEnemy.Position);

                        enemyState[`enemy${index}`] = {
                            id: `enemy${index}`,
                            name: initialEnemy.name,
                            level: initialEnemy.level,
                            HP: initialEnemy.HP,
                            CurHP: currentEnemy ? currentEnemy.CurHP : 0,
                            MP: initialEnemy.MP,
                            CurMP: currentEnemy ? currentEnemy.CurMP : initialEnemy.MP,
                            EXP: initialEnemy.EXP,
                            CurEXP: initialEnemy.CurEXP,
                            ATK: initialEnemy.ATK,
                            DEF: initialEnemy.DEF,
                            Move: initialEnemy.Move,
                            Position: initialEnemy.Position,
                            ATKRange: initialEnemy.ATKRange,
                            AvoidRate: initialEnemy.AvoidRate,
                            HitRate: initialEnemy.HitRate,
                            CriticalHitRate: initialEnemy.CriticalHitRate,
                            AlreadyMove: currentEnemy ? currentEnemy.AlreadyMove : false,
                            "是否蓄力": currentEnemy ? currentEnemy["是否蓄力"] : false,
                            "是否釋放蓄力": currentEnemy ? currentEnemy["是否釋放蓄力"] : false,
                            "已增加移動力": currentEnemy ? currentEnemy["已增加移動力"] : false,
                            Equipment: {
                                Weapon: initialEnemy.Equipment.Weapon ? { ...initialEnemy.Equipment.Weapon } : null,
                                Armor: initialEnemy.Equipment.Armor ? { ...initialEnemy.Equipment.Armor } : null,
                                Fitting: initialEnemy.Equipment.Fitting ? { ...initialEnemy.Equipment.Fitting } : null
                            },
                            Inventory: initialEnemy.Inventory ? initialEnemy.Inventory.map(item => ({ ...item })) : [],
                            RewardItems: initialEnemy.RewardItems ? initialEnemy.RewardItems.map(item => ({ ...item })) : [],
                            五內: initialEnemy.五內 ? { ...initialEnemy.五內 } : {},
                            法抗: initialEnemy.法抗 ? { ...initialEnemy.法抗 } : {},
                            法術: initialEnemy.法術 ? initialEnemy.法術.map(spell => ({
                                name: spell.name,
                                type: spell.type,
                                Classify: spell.Classify,
                                NeedMove: spell.NeedMove,
                                NeedLV: spell.NeedLV,
                                Range: spell.Range,
                                distance: spell.distance, // 保存法術的施行距離
                                Rmdistance: spell.Rmdistance, // 保存法術的最小距離
                                NeedMP: spell.NeedMP,
                                NeedSoul: { ...spell.NeedSoul },
                                Icon: spell.Icon,
                                Animations: spell.Animations,
                                Sounds: spell.Sounds
                            })) : [],
                            圖片: initialEnemy.圖片,
                            MoveRes: { ...initialEnemy.MoveRes },
                            AtkRes: { ...initialEnemy.AtkRes },
                            遭受傷害: { ...initialEnemy.遭受傷害 },
                            AtkMiss: { ...initialEnemy.AtkMiss }
                        };
                    });
                }

                // 構建完整的寶箱數據（包括已拿取的）
                let allTreasures = [];
                if (controlLayer[currentLevel] && controlLayer[currentLevel].Treasures) {
                    const takenTreasures = typeof window.takenTreasures !== 'undefined' ? window.takenTreasures : new Set();

                    allTreasures = controlLayer[currentLevel].Treasures.map(treasure => ({
                        "位置": treasure["位置"],
                        "寶物": treasure["寶物"],
                        "是否已拿取": takenTreasures.has(treasure["位置"])
                    }));
                }

                let saveData = {
                    currentLevel,
                    allPlayers: cleanPlayers,
                    enemy: enemyState,
                    treasures: allTreasures,
                    takenTreasures: typeof window.takenTreasures !== 'undefined' ? Array.from(window.takenTreasures) : [],
                    currentRound: runOBJ["回合"],
                    timestamp: new Date().toISOString()
                };
                localStorage.setItem(`saveSlot${i}`, JSON.stringify(saveData));

                let timestamp = new Date(saveData.timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                let levelTitle = controlLayer[saveData.currentLevel]["標題"] || `關卡 ${saveData.currentLevel}`;
                playerLevel.style.display = "block";
                playerLevel.textContent = saveData.allPlayers
                    ? `位階： ${saveData.allPlayers.player1.level}`
                    : "位階： ";
                saveTime.textContent = timestamp;
                title.textContent = levelTitle;
                alert(`存檔成功！`);
            }
        });

        // 添加動畫
        const delay = i * 0.2;
        slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

        slotsContainer.appendChild(slot);
    }

    saveContent.appendChild(slotsContainer);
    saveDialog.appendChild(saveContent);
    Dom.GameMap.appendChild(saveDialog);
    saveDialog.showModal();
}

function loadGameFunc() {
    return new Promise((resolve) => {
        console.log("loadGameFunc 開始執行");

        // 檢查當前場景和狀態
        if (typeof sceneManager !== 'undefined' && sceneManager.sceneType === 'camp') {
            console.log("在營地場景中執行讀檔");
            // 在營地場景中允許讀檔
        } else if (typeof runOBJ !== 'undefined') {
            // 在戰鬥場景中的檢查
            if (runOBJ["當前行動方"] === "Enemys") {
                console.log("敵人行動中，無法讀檔");
                resolve({ loadgame: false });
                return;
            }
            // 允許在以下狀態讀檔：
            // - null (無操作)
            // - "選擇移動格子中" (顯示移動範圍時)
            // 禁止在以下狀態讀檔：
            // - "正在移動中", "已經移動但未進行操作", "攻擊中", "選擇攻擊目標中"
            const blockedStates = ["正在移動中", "已經移動但未進行操作", "攻擊中", "選擇攻擊目標中"];
            if (blockedStates.includes(runOBJ["當前操作"])) {
                console.log(`當前操作狀態 "${runOBJ["當前操作"]}" 不允許讀檔`);
                resolve({ loadgame: false });
                return;
            }
        } else {
            console.log("在主選單或其他場景中執行讀檔");
        }

        // 調試：檢查 localStorage 中的存檔
        console.log("檢查 localStorage 中的存檔:");
        for (let i = 0; i < 5; i++) {
            // 檢查 saveSlot 格式
            const saveSlotKey = `saveSlot${i}`;
            const saveSlotData = localStorage.getItem(saveSlotKey);
            console.log(`${saveSlotKey}:`, saveSlotData ? "有數據" : "無數據");

            if (saveSlotData) {
                try {
                    const parsed = JSON.parse(saveSlotData);
                    console.log(`槽位 ${i} (saveSlot格式) 詳細信息:`, {
                        currentLevel: parsed.currentLevel,
                        levelTitle: controlLayer[parsed.currentLevel]?.["標題"] || `關卡 ${parsed.currentLevel}`,
                        timestamp: new Date(parsed.timestamp).toLocaleString(),
                        hasAllPlayers: !!parsed.allPlayers,
                        hasEnemy: !!parsed.enemy
                    });
                } catch (e) {
                    console.error(`槽位 ${i} (saveSlot格式) 解析錯誤:`, e);
                }
            }

            // 檢查 battleSave 格式
            const battleSaveKey = `battleSave_${i}`;
            const battleSaveData = localStorage.getItem(battleSaveKey);
            console.log(`${battleSaveKey}:`, battleSaveData ? "有數據" : "無數據");

            if (battleSaveData) {
                try {
                    const parsed = JSON.parse(battleSaveData);
                    console.log(`槽位 ${i} (battleSave格式) 詳細信息:`, {
                        levelTitle: parsed.levelTitle,
                        timestamp: new Date(parsed.timestamp).toLocaleString(),
                        playersCount: parsed.players?.length || 0,
                        enemiesCount: parsed.enemies?.length || 0
                    });
                } catch (e) {
                    console.error(`槽位 ${i} (battleSave格式) 解析錯誤:`, e);
                }
            }
        }
        // 創建 dialog 元素
        let loadDialog = document.createElement("dialog");
        loadDialog.id = "loadgame-dialog";

        // 創建 dialog 內容容器
        let loadContent = document.createElement("div");
        loadContent.className = "save-content";

        // 添加標題
        let title = document.createElement("div");
        title.className = "save-title";
        title.innerHTML = "讀取進度";
        loadContent.appendChild(title);

        // 添加關閉按鈕
        let closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.addEventListener("click", () => {
            loadDialog.close();
            Dom.GameMap.removeChild(loadDialog);
            resolve({ loadgame: false });
        });
        loadContent.appendChild(closeButton);

        // 創建存檔槽容器
        let slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            let slot = document.createElement("div");
            slot.className = "save-slot";

            // 檢查存檔數據 - 優先檢查 saveSlot 格式（與 saveGameFunc 一致）
            let saveData = null;
            try {
                // 首先檢查 saveSlot 格式（Main.js saveGameFunc 使用的格式）
                const saveSlotKey = `saveSlot${i}`;
                let saveSlotData = localStorage.getItem(saveSlotKey);

                if (saveSlotData) {
                    saveData = JSON.parse(saveSlotData);
                    console.log(`找到 saveSlot 格式存檔槽 ${i} 的數據:`, saveData);
                } else {
                    // 如果沒有 saveSlot 格式，再檢查 battleSave 格式
                    const battleSaveKey = `battleSave_${i}`;
                    const battleSaveData = localStorage.getItem(battleSaveKey);
                    if (battleSaveData) {
                        saveData = JSON.parse(battleSaveData);
                        console.log(`找到 battleSave 格式存檔槽 ${i} 的數據:`, saveData);
                    }
                }
            } catch (e) {
                console.warn(`讀取存檔槽 ${i} 失敗:`, e);
            }

            // 左欄：圖騰
            let totem = document.createElement("div");
            totem.className = "save-slot-totem";
            let totemImage = document.createElement("img");
            totemImage.draggable = false;
            totemImage.src = saveData ? "./Public/saveicon.png" : "./Public/saveicon.png";
            totem.appendChild(totemImage);
            slot.appendChild(totem);

            // 中間欄：玩家等級和存檔時間
            let middle = document.createElement("div");
            middle.className = "save-slot-middle";
            let playerLevel = document.createElement("div");
            playerLevel.className = "save-slot-level";
            let saveTime = document.createElement("div");
            saveTime.className = "save-slot-time";

            if (saveData) {
                let playerLevel_text, saveTime_text, levelTitle;

                // 根據存檔格式解析數據
                if (saveData.allPlayers) {
                    // saveSlot 格式
                    const firstPlayer = saveData.allPlayers.player1 || saveData.allPlayers.player0 || saveData.allPlayers.player2;
                    playerLevel_text = firstPlayer
                        ? `位階： ${firstPlayer.level || 1}`
                        : "位階： 無";
                    saveTime_text = new Date(saveData.timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                    levelTitle = controlLayer[saveData.currentLevel]?.["標題"] || `關卡 ${saveData.currentLevel}`;
                    console.log(`存檔槽 ${i} (saveSlot格式) 顯示信息: 關卡=${levelTitle}, 玩家等級=${firstPlayer?.level}, 時間=${new Date(saveData.timestamp).toLocaleString()}`);
                } else if (saveData.players) {
                    // battleSave 格式
                    const firstPlayer = saveData.players[0];
                    playerLevel_text = firstPlayer
                        ? `位階： ${firstPlayer.level || 1}`
                        : "位階： 無";
                    saveTime_text = new Date(saveData.timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                    levelTitle = saveData.levelTitle;
                    console.log(`存檔槽 ${i} (battleSave格式) 顯示信息: 關卡=${levelTitle}, 玩家等級=${firstPlayer?.level}, 時間=${new Date(saveData.timestamp).toLocaleString()}`);
                } else {
                    // 未知格式
                    playerLevel_text = "位階： 未知";
                    saveTime_text = new Date(saveData.timestamp || Date.now()).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                    levelTitle = "未知關卡";
                    console.log(`存檔槽 ${i} (未知格式) 顯示信息`);
                }

                playerLevel.textContent = playerLevel_text;
                saveTime.textContent = saveTime_text;

                // 標記為有存檔
                slot.classList.add("has-save");
            } else {
                playerLevel.textContent = "空存檔";
                saveTime.textContent = "";
                console.log(`存檔槽 ${i} 為空`);

                // 標記為空存檔
                slot.classList.add("empty-save");
            }
            middle.appendChild(playerLevel);
            middle.appendChild(saveTime);
            slot.appendChild(middle);

            // 右欄：關卡標題
            let title = document.createElement("div");
            title.className = "save-slot-title";

            if (saveData) {
                if (saveData.allPlayers) {
                    // saveSlot 格式
                    title.textContent = controlLayer[saveData.currentLevel]?.["標題"] || `關卡 ${saveData.currentLevel}`;
                } else if (saveData.levelTitle) {
                    // battleSave 格式
                    title.textContent = saveData.levelTitle;
                } else {
                    title.textContent = "未知關卡";
                }
            } else {
                title.textContent = "空存檔";
            }

            slot.appendChild(title);

            // 存檔槽點擊事件
            slot.addEventListener("click", () => {
                if (!saveData) {
                    return;
                }

                let confirmLoad = confirm(`是否要讀取第 ${i + 1} 格的存檔？`);
                if (confirmLoad) {
                    loadDialog.close();
                    Dom.GameMap.removeChild(loadDialog);

                    // 使用新的 Game.loadGame 方法
                    const success = Game.loadGame(i);
                    if (success) {
                        alert("存檔讀取成功！");
                        resolve({ loadgame: true });
                    } else {
                        alert("存檔讀取失敗！");
                        resolve({ loadgame: false });
                    }
                }
            });

            // 添加動畫
            const delay = i * 0.2;
            slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

            slotsContainer.appendChild(slot);
        }

        loadContent.appendChild(slotsContainer);
        loadDialog.appendChild(loadContent);
        Dom.GameMap.appendChild(loadDialog);
        loadDialog.showModal();
    });
}

//防斗
function debounce(func, delay) {
    let timer;
    return function (...args) {
        const context = this;

        clearTimeout(timer);

        timer = setTimeout(() => {
            func.apply(context, args);
        }, delay);
    };
}











