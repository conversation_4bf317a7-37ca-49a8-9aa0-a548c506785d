function turntochinese(turn) {
    // turn的數字轉中文   
    let chinese = ["一", "二", "三", "四", "五", "六", "七", "八", "九"];
    let turnchinese = "";

    if (turn === 10) {
        turnchinese = "十";
        return turnchinese;
    }

    if(turn > 99){
        turnchinese = "九十九";
        return turnchinese;
    }


    if (turn < 10) {
        turnchinese = chinese[turn - 1];
    } else {
        let ten = Math.floor(turn / 10);
        let one = turn % 10;
        if (ten === 1) {
            turnchinese = "十" + chinese[one - 1];
        } else {
            turnchinese = chinese[ten - 1] + "十" + chinese[one - 1];
        }
    }

    return turnchinese;
}

function TurnTextHandle() {
    let turn = runOBJ["回合"];
    let operator = runOBJ["當前行動方"];
    let turnchinese = turntochinese(turn);

    if (operator === "Players") {
        let turntext = document.createElement("div");
        turntext.classList.add("turntext");
        turntext.style.animation = "turnappear 1.5s linear forwards";
        turntext.innerHTML = `
            <p>陽之陣 其${turnchinese}</p>
        `
        Dom.GameScreen.appendChild(turntext);

        setTimeout(() => {
            turntext.style.animation = "turndisappear 1s linear forwards";
            setTimeout(() => {
                Dom.GameScreen.removeChild(turntext);
            }, 2000);
        }, 2000);



    } else {
        let turntext = document.createElement("div");
        turntext.classList.add("turntext");
        turntext.style.animation = "turnappear 1s linear forwards";
        turntext.innerHTML = `
            <p>陰之陣 其${turnchinese}</p>
        `
        Dom.GameScreen.appendChild(turntext);

        setTimeout(() => {
            turntext.style.animation = "turndisappear 1s linear forwards";
            setTimeout(() => {
                Dom.GameScreen.removeChild(turntext);
            }, 2000);
        }, 2000);
    }
}