//敵人攻擊命中
async function enemyAttackfirst(player, enemy, playerCanvas, enemyCanvas) {
    // 計算裝備加成後的防禦力
    let totalDEF = player.DEF;
    if (player.Equipment.Armor) {
        totalDEF += player.Equipment.Armor.DEF;
    }
    if (player.Equipment.Fitting && player.Equipment.Fitting.effect?.DEF) {
        totalDEF += player.Equipment.Fitting.effect.DEF;
    }

    let enemycriticalhitrate = enemy.CriticalHitRate + Math.floor(Math.random() * (50 - enemy.CriticalHitRate / 2));


    if (enemycriticalhitrate >= 100) {
        let atktwotimes = Math.random() > 0.5; // 隨機決定是否連續攻擊兩次

        if (atktwotimes) {
            let enemydamage = Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK * 0.8 - totalDEF) < 0
                ? 1 : Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK * 0.8 - totalDEF);


            await wait(0)
            setenemycriticalimg(enemyCanvas)
            drawPlayerIdleAnimation(player, playerCanvas, enemyCanvas)     // 設置玩家站姿圖片
            drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)       // 設置敵人站姿圖片


            await wait(0.8)
            drawEnemyAttackAnimation(enemy, enemyCanvas, playerCanvas)
            await enemy.bloodfun(enemydamage, player)

            await wait(1.5)
            drawEnemyAttackAnimation(enemy, enemyCanvas, playerCanvas)
            const playerisdied = await enemy.bloodfun(enemydamage, player)

            await wait(0.25)
            setenemydamagetext(enemydamage * 2);

            return { playerisdied };
        } else {
            let enemydamage = Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK * 1.8 - totalDEF) < 0
                ? 1 : Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK * 1.8 - totalDEF);


            await wait(0)
            setenemycriticalimg(enemyCanvas)
            drawPlayerIdleAnimation(player, playerCanvas, enemyCanvas)     // 設置玩家站姿圖片
            drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)       // 設置敵人站姿圖片

            await wait(0.8)
            drawEnemyAttackAnimation(enemy, enemyCanvas, playerCanvas)
            const playerisdied = await enemy.bloodfun(enemydamage, player)

            await wait(0.25)
            setenemydamagetext(enemydamage);


            return { playerisdied };
        }
    } else {
        let enemydamage = Math.floor(Math.random() * (enemy.ATK - totalDEF) + enemy.ATK * 0.98 - totalDEF) < 0
            ? 1 : Math.floor(Math.random() * (20) + enemy.ATK * 0.98 - totalDEF);



        await wait(0)
        drawPlayerIdleAnimation(player, playerCanvas, enemyCanvas)     // 設置玩家站姿圖片
        drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)       // 設置敵人站姿圖片

        await wait(0.6)
        drawEnemyAttackAnimation(enemy, enemyCanvas, playerCanvas)
        const playerisdied = await enemy.bloodfun(enemydamage, player)

        await wait(0.25)
        setenemydamagetext(enemydamage);

        return { playerisdied };
    }
}

//敵人攻擊未命中
async function enemyAttackfirstMiss(player, enemy, playerCanvas, enemyCanvas) {
    let enemydamage = 0;
    let playerisdied = false;

    await wait(0)
    drawEnemyAttackMissAnimation(enemy, enemyCanvas, playerCanvas)
    drawPlayerIdleAnimation(player, playerCanvas, enemyCanvas)

    await wait(1)
    setenemydamagetext(enemydamage)


    return { playerisdied };
}

//玩家攻擊命中
async function playerAttacksecond(player, enemy, playerCanvas, enemyCanvas) {
    // 計算裝備加成後的攻擊力
    let totalATK = player.ATK;
    if (player.Equipment.Weapon) {
        totalATK += player.Equipment.Weapon.ATK;
    }
    if (player.Equipment.Fitting && player.Equipment.Fitting.effect?.ATK) {
        totalATK += player.Equipment.Fitting.effect.ATK;
    }

    let playercriticalhitrate = player.CriticalHitRate + Math.floor(Math.random() * (100 - player.CriticalHitRate));

    if (playercriticalhitrate >= 100) {
        let atktwotimes = Math.random() > 0.5; // 隨機決定是否連續攻擊兩次

        if (atktwotimes) {
            let playerdamage = Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 1.8 - enemy.DEF) < 0
                ? 1 : Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 1.8 - enemy.DEF);

            await wait(3);
            setplayercriticalimg(playerCanvas)                  // 設置玩家暴擊特效圖片
            drawPlayerATKAnimation(player, playerCanvas, enemyCanvas);  // 設置玩家攻擊圖片
            drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)       // 設置敵人站姿圖片

            await wait(0.05);
            player.function(); // 攻擊畫面震動特效

            await wait(0.1);
            await player.bloodfun(playerdamage, enemy); // 血條動畫特效，最後回傳是否敵人死亡

            await wait(0.5);
            drawPlayerATKAnimation(player, playerCanvas, enemyCanvas);  // 設置玩家攻擊圖片

            await wait(0.05);
            player.function(); // 攻擊畫面震動特效

            await wait(0.1);
            const isDead = await player.bloodfun(playerdamage, enemy); // 血條動畫特效，最後回傳是否敵人死亡

            await wait(0.25);
            setplayerdamagetext(playerdamage * 2); // 設置玩家傷害數字顯示

            return { isDead };

        } else {
            let playerdamage = Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 1.8 - enemy.DEF) < 0
                ? 1 : Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 1.8 - enemy.DEF);



            await wait(3);
            setplayercriticalimg(playerCanvas)                  // 設置玩家暴擊特效圖片
            drawPlayerATKAnimation(player, playerCanvas, enemyCanvas);  // 設置玩家攻擊圖片
            drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)       // 設置敵人站姿圖片

            await wait(0.05);
            player.function(); // 攻擊畫面震動特效

            await wait(0.1);
            const isDead = await player.bloodfun(playerdamage, enemy); // 血條動畫特效，最後回傳是否敵人死亡

            await wait(0.25);
            setplayerdamagetext(playerdamage); // 設置玩家傷害數字顯示
            return { isDead };
        }

    } else {
        let playerdamage = Math.floor(Math.random() * (totalATK - enemy.DEF) + totalATK * 0.98 - enemy.DEF) < 0
            ? 1 : Math.floor(Math.random() * (20) + totalATK * 0.98 - enemy.DEF);

        await wait(3);
        drawPlayerATKAnimation(player, playerCanvas, enemyCanvas)
        drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)       // 設置敵人站姿圖片

        await wait(0.05); // 等待0.05秒
        player.function()

        await wait(0.1)
        const isDead = await player.bloodfun(playerdamage, enemy)

        await wait(0.25);
        setplayerdamagetext(playerdamage)

        return { isDead };
    }


}

//玩家攻擊未命中
async function playerAttacksecondMiss(player, enemy, playerCanvas, enemyCanvas) {
    let playerdamage = 0;
    let isDead = false;

    await wait(3)
    drawEnemyIdleAnimation(enemy, enemyCanvas, playerCanvas)       // 設置敵人站姿圖片

    await wait(1.5)
    drawPlayerATKMissAnimation(player, playerCanvas, enemyCanvas)

    await wait(1.5)
    setplayerdamagetext(playerdamage)

    return { isDead }
}
