#savegame-dialog, #loadgame-dialog, #save-battle-dialog, #load-battle-dialog {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 94.5%;
    height: 100%;
    background-image: url(../Public/savegamebg.png);
    background-size: cover;
    background-position: center;
    border: none;
    border-radius: 10px;
    padding: 20px;
    box-sizing: border-box;
    z-index: 10000;
   
}

#loadgame-dialog::backdrop, #save-battle-dialog::backdrop, #load-battle-dialog::backdrop {
    background-color: black;
}

.save-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    position: relative;
    width: 100%;
    height: 100%;
}

.save-title {
    font-size: 32px;
    color: rgb(168, 105, 38);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    margin-bottom: 20px;
    background-image: url(../Public/showskillbg.png);
    background-size: 100%;
    background-repeat: no-repeat;
    width: 300px;
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
}

.save-close-btn {
    position: absolute;
    top: 1px;
    left: 1px;
    padding: 5px 15px;
    font-size: 35px;
    font-weight: 600;
    background-color: rgb(247, 231, 173);
    color: rgb(168, 105, 38);
    border: 5px solid rgb(165, 90, 24);
    border-radius: 5px;
    cursor: pointer;
}

.save-slots-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    flex-grow: 1;
    overflow-y: auto;
    padding-top: 20px;
}

.save-slot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 110px;
    background-color: rgb(247, 231, 173);
    border: 5px solid rgb(165, 90, 24);
    border-radius: 5px;
    padding: 5px;
    cursor: pointer;
    box-sizing: border-box;
    opacity: 0;
}

.save-slot-totem {
    width: 30%;
    text-align: center;
}

.save-slot-totem img {
    width: 360px;
    height: 95px;
}

.save-slot-middle {
    width: 30%;
    text-align: center;
}

.save-slot-level {
    color: rgb(168, 105, 38);
    font-size: 25px;
}

.save-slot-time {
    color: rgb(168, 105, 38);
    font-size: 20px;
}

.save-slot-title {
    width: 30%;
    text-align: center;
    font-weight: 600;
    color: rgb(168, 105, 38);
    font-size: 28px;
}

@keyframes slideIn {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
} 