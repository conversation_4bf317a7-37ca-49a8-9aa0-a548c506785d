/**
 * 關卡場景類 - 實現關卡場景管理、通關條件檢測和場景切換邏輯
 */
class LevelScene {
    constructor(levelIndex) {
        this.levelIndex = levelIndex;
        this.levelData = controlLayer[levelIndex];
        this.isActive = false;

        console.log(`LevelScene 初始化: 關卡 ${levelIndex}`, this.levelData);
    }

    // 初始化關卡場景
    init() {
        console.log(`初始化關卡場景 ${this.levelIndex}: ${this.levelData["標題"]}`);

        this.isActive = true;

        // 初始化關卡遊戲邏輯
        this.initGameLogic();

        console.log("關卡場景初始化完成");
    }

    // 初始化遊戲邏輯
    initGameLogic() {
        // 這裡調用原有的 Game.init() 邏輯
        if (typeof Game !== 'undefined') {
            Game.init();
        }
    }



    // 檢查失敗條件
    checkDefeatCondition() {
        if (!this.isActive) return;

        // 檢查是否所有玩家都死亡
        if (typeof gameplayers !== 'undefined') {
            const alivePlayers = gameplayers.filter(player => player.CurHP > 0);
            if (alivePlayers.length === 0) {
                console.log("所有玩家死亡，遊戲結束");
                this.handleDefeat();
            }
        }
    }



    // 處理失敗
    handleDefeat() {
        console.log(`關卡 ${this.levelIndex} 失敗!`);

        this.isActive = false;

        // 顯示失敗訊息
        this.showDefeatMessage();

        // 可以觸發遊戲結束事件或重新開始選項
    }



    // 顯示失敗訊息
    showDefeatMessage() {
        const defeatDialog = document.createElement("dialog");
        defeatDialog.id = "defeatDialog";
        defeatDialog.innerHTML = `
            <div style="padding: 20px; text-align: center; background: linear-gradient(45deg, #FF6B6B, #FF4757); border-radius: 10px;">
                <h2 style="color: white; margin-bottom: 15px;">💀 任務失敗 💀</h2>
                <p style="color: white; font-size: 16px; margin-bottom: 20px;">${this.levelData["失敗訊息"]}</p>
                <button id="retryBtn" style="padding: 10px 20px; font-size: 16px; background: #FFA500; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    重新挑戰
                </button>
                <button id="exitBtn" style="padding: 10px 20px; font-size: 16px; background: #6C5CE7; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    返回主選單
                </button>
            </div>
        `;

        document.body.appendChild(defeatDialog);
        defeatDialog.showModal();

        // 設置按鈕事件
        document.getElementById("retryBtn").onclick = () => {
            defeatDialog.close();
            document.body.removeChild(defeatDialog);
            // 重新開始當前關卡
            sceneManager.switchToLevel(this.levelIndex);
        };

        document.getElementById("exitBtn").onclick = () => {
            defeatDialog.close();
            document.body.removeChild(defeatDialog);
            // 返回主選單
            window.location.reload();
        };
    }

    // 獲取關卡資料
    getLevelData() {
        return this.levelData;
    }

    // 獲取關卡標題
    getTitle() {
        return this.levelData["標題"];
    }

    // 獲取關卡索引
    getLevelIndex() {
        return this.levelIndex;
    }

    // 停止場景
    stop() {
        console.log(`停止關卡場景 ${this.levelIndex}`);

        this.isActive = false;

        // 清理勝利條件檢查
        if (this.victoryCheckInterval) {
            clearInterval(this.victoryCheckInterval);
            this.victoryCheckInterval = null;
        }

        // 清理遊戲邏輯
        if (typeof Game !== 'undefined' && typeof Game.cleanup === 'function') {
            Game.cleanup();
        }
    }

    // 清理資源
    cleanup() {
        this.stop();
        console.log(`關卡場景 ${this.levelIndex} 清理完成`);
    }
}

// 導出關卡場景類（如果需要模組化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LevelScene;
}
