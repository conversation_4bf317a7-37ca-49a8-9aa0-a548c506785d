// AI玩家邏輯實現 - Canvas版本，參考init.js和players.js
function PlayerAIAction() {
    console.log("開始AI玩家行動");

    let players = gameplayers;
    let enemys = gameenemys;
    clearAllHighlights();

    // 清除敵人高亮（如果存在此函數）
    if (typeof clearAllEnemyHighlights === 'function') {
        clearAllEnemyHighlights();
    }

    // 添加遮罩層禁止點擊
    let overlay = document.createElement("div");
    overlay.style.position = "fixed";
    overlay.style.top = "0";
    overlay.style.left = "0";
    overlay.style.width = "100%";
    overlay.style.height = "100%";
    overlay.style.zIndex = "9999";
    overlay.style.backgroundColor = "rgba(0, 0, 0, 0.1)"; // 輕微遮罩效果
    document.body.appendChild(overlay);

    // 添加點擊事件監聽器來阻止其他操作
    overlay.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        return false;
    });

    if (players.length === 0 || runOBJ["當前行動方"] === "Enemys" || enemys.length === 0) {
        document.body.removeChild(overlay);
        return;
    }

    let actionIndex = 0;
    let isActionInProgress = false;

    // 立即開始行動
    processPlayerAction();

    async function processPlayerAction() {
        console.log(`AI玩家行動處理，當前索引: ${actionIndex}`);

        // 檢查是否有玩家死亡
        let diedPlayers = gameplayers.filter(player => player.CurHP <= 0 && player["是否電腦操作"] === false);

        if (diedPlayers.length !== 0) {
            console.log("有玩家死亡，結束AI行動");
            document.body.removeChild(overlay);
            return;
        }

        // 跳過已死亡的AI玩家，找到下一個活著的AI玩家
        while (actionIndex < players.length &&
               (players[actionIndex].CurHP <= 0 || !players[actionIndex]["是否電腦操作"])) {
            console.log(`跳過玩家 ${actionIndex}: 死亡或非AI控制`);
            actionIndex++;
        }

        // 檢查是否所有AI玩家都已行動完畢
        if (actionIndex >= players.length) {
            console.log("所有AI玩家行動完畢");
            // 重置敵人狀態（如果需要）
            enemys.forEach(enemy => {
                enemy.AlreadyMove = false;
            });

            // 移除遮罩層並讓玩家行動
            document.body.removeChild(overlay);
            setTimeout(() => {
                operates.MoveComera(gameplayers[0].Position);
            }, 1000);
            return;
        }

        let player = players[actionIndex];
        console.log(`AI玩家 ${player.name} 開始行動`);

        // 雙重檢查玩家狀態
        if (player.CurHP <= 0 || !player["是否電腦操作"]) {
            console.log(`玩家 ${player.name} 無法行動，跳過`);
            actionIndex++;
            processPlayerAction();
            return;
        }

        // 獲取移動範圍 - 使用canvas兼容的BFS
        let playerMoveRange = getPlayerMoveRange(player.Position, player.Move, actionIndex);

        // 找出最弱的敵人（使用戰力計算：防禦+生命）
        let weakestEnemy = findWeakestEnemyByPower(enemys);

        // 找出最近的敵人
        let closestEnemy = findClosestEnemy(player.Position, enemys);

        console.log(`AI玩家 ${player.name} 分析目標: 最弱敵人 ${weakestEnemy?.name}, 最近敵人 ${closestEnemy?.name}`);

        // 檢查是否可以直接攻擊最弱的敵人
        if (isEnemyInRange(player, weakestEnemy)) {
            console.log(`AI玩家 ${player.name} 可以直接攻擊最弱敵人 ${weakestEnemy.name}`);
            operates.MoveComera(weakestEnemy.Position);

            await wait(1);

            // 戰鬥前讓AI玩家和敵人面向對方
            await setAIPlayerBattleFacingDirection(player, weakestEnemy, actionIndex);
            
            await wait(1);

            let { battleisended } = await AccessBattle(player, weakestEnemy, actionIndex, gameenemys.indexOf(weakestEnemy));

            // 設置AI玩家已行動
            player.AlreadyMove = true;
            console.log(`AI玩家 ${player.name} 設置為已行動 (AlreadyMove = true)`);

            // 無論戰鬥結果如何，都繼續下一個AI玩家
            await wait(2);
            actionIndex++;
            isActionInProgress = false;
            console.log(`AI玩家 ${player.name} 攻擊完成，battleisended: ${battleisended}`);
            processPlayerAction();
            return;
        }

        // 檢查是否可以直接攻擊最近的敵人
        if (isEnemyInRange(player, closestEnemy)) {
            console.log(`AI玩家 ${player.name} 可以直接攻擊最近敵人 ${closestEnemy.name}`);
            operates.MoveComera(closestEnemy.Position);

            await wait(1);

            // 戰鬥前讓AI玩家和敵人面向對方
            await setAIPlayerBattleFacingDirection(player, closestEnemy, actionIndex);

            await wait(1);

            let { battleisended } = await AccessBattle(player, closestEnemy, actionIndex, gameenemys.indexOf(closestEnemy));

            // 設置AI玩家已行動
            player.AlreadyMove = true;
            console.log(`AI玩家 ${player.name} 設置為已行動 (AlreadyMove = true)`);

            // 無論戰鬥結果如何，都繼續下一個AI玩家
            await wait(2);
            actionIndex++;
            isActionInProgress = false;
            console.log(`AI玩家 ${player.name} 攻擊完成，battleisended: ${battleisended}`);
            processPlayerAction();
            return;
        }

        // 尋找可以攻擊到敵人的位置
        let bestPosition = findBestAttackPosition(player, playerMoveRange, enemys);

        if (bestPosition.position && bestPosition.target) {
            console.log("找到可以攻擊的位置:", bestPosition.position);
            if (!isActionInProgress) {
                isActionInProgress = true;
                setTimeout(() => {
                    animateAIPlayerMove(findPlayerPath(player.Position, bestPosition.position, player.Move, actionIndex), actionIndex, false);
                    setTimeout(() => {
                        operates.MoveComera(bestPosition.position);
                        setTimeout(async () => {
                            if (isEnemyInRange({ ...player, Position: bestPosition.position }, bestPosition.target)) {
                                // 戰鬥前讓AI玩家和敵人面向對方
                                await setAIPlayerBattleFacingDirection(player, bestPosition.target, actionIndex);

                                await wait(1);

                                let { battleisended } = await AccessBattle(player, bestPosition.target, actionIndex, gameenemys.indexOf(bestPosition.target));

                                // 設置AI玩家已行動
                                player.AlreadyMove = true;
                                console.log(`AI玩家 ${player.name} 移動後攻擊，設置為已行動 (AlreadyMove = true)`);

                                await wait(2);
                                actionIndex++;
                                isActionInProgress = false;
                                console.log(`AI玩家 ${player.name} 移動後攻擊完成，battleisended: ${battleisended}`);
                                processPlayerAction();
                            } else {
                                // 即使無法攻擊，移動後也要設置已行動
                                player.AlreadyMove = true;
                                console.log(`AI玩家 ${player.name} 移動後無法攻擊，設置為已行動 (AlreadyMove = true)`);

                                actionIndex++;
                                isActionInProgress = false;
                                processPlayerAction();
                            }
                        }, 500);
                    }, 2450);
                }, 1000);
            }
            return;
        }

        // 如果無法攻擊，則移動到最近的敵人附近
        let closestPosition = findClosestPositionToEnemy(playerMoveRange, closestEnemy.Position);

        if (closestPosition) {
            console.log("移動到最近的敵人附近:", closestPosition);
            setTimeout(() => {
                animateAIPlayerMove(findPlayerPath(player.Position, closestPosition, player.Move, actionIndex), actionIndex, false);
                setTimeout(() => {
                    operates.MoveComera(closestPosition);
                    setTimeout(async () => {
                        if (isEnemyInRange({ ...player, Position: closestPosition }, closestEnemy)) {
                            // 戰鬥前讓AI玩家和敵人面向對方
                            await setAIPlayerBattleFacingDirection(player, closestEnemy, actionIndex);

                            await wait(1);

                            let { battleisended } = await AccessBattle(player, closestEnemy, actionIndex, gameenemys.indexOf(closestEnemy));

                            // 設置AI玩家已行動
                            player.AlreadyMove = true;
                            console.log(`AI玩家 ${player.name} 接近後攻擊，設置為已行動 (AlreadyMove = true)`);

                            await wait(2);
                            actionIndex++;
                            isActionInProgress = false;
                            console.log(`AI玩家 ${player.name} 接近後攻擊完成，battleisended: ${battleisended}`);
                            processPlayerAction();
                        } else {
                            // 即使無法攻擊，移動後也要設置已行動
                            player.AlreadyMove = true;
                            console.log(`AI玩家 ${player.name} 接近後無法攻擊，設置為已行動 (AlreadyMove = true)`);

                            actionIndex++;
                            isActionInProgress = false;
                            processPlayerAction();
                        }
                    }, 500);
                }, 2450);
            });
        } else {
            actionIndex++;
            processPlayerAction();
        }
    }
}

// 獲取玩家移動範圍
function getPlayerMoveRange(startPosition, moveRange, playerIndex) {
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    // 獲取所有敵人的位置
    const enemyPositions = gameenemys
        .filter(enemy => enemy.CurHP > 0)
        .map(enemy => enemy.Position);

    // 獲取所有敵人周圍的格子（上下左右）
    const enemyAdjacentCells = new Set();
    enemyPositions.forEach(enemyPos => {
        const enemyRow = Math.floor(enemyPos / controlLayer[currentLevel].size.cols);
        const enemyCol = enemyPos % controlLayer[currentLevel].size.cols;

        directions.forEach(dir => {
            const newRow = enemyRow + dir.y;
            const newCol = enemyCol + dir.x;
            if (newRow >= 0 && newRow < controlLayer[currentLevel].size.rows &&
                newCol >= 0 && newCol < controlLayer[currentLevel].size.cols) {
                const adjacentPos = newRow * controlLayer[currentLevel].size.cols + newCol;
                enemyAdjacentCells.add(adjacentPos);
            }
        });
    });

    const queue = [{ position: startPosition, distance: 0 }];
    const visited = new Set();
    visited.add(startPosition);

    // 用於存儲所有可到達的格子及其距離
    const reachableCells = new Map();
    reachableCells.set(startPosition, 0);

    while (queue.length > 0) {
        const { position, distance } = queue.shift();

        if (distance < moveRange) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    // 檢查是否為隊友位置
                    const isTeammatePosition = gameplayers.some((player, index) =>
                        index !== playerIndex && player.Position === newPosition
                    );

                    if (
                        !visited.has(newPosition) &&
                        !gameobstacles.includes(newPosition) &&
                        !gameenemys.some(enemy => enemy.Position === newPosition && enemy.CurHP > 0) &&
                        (!enemyAdjacentCells.has(newPosition) || isTeammatePosition)  // 如果是隊友位置，允許穿過敵人周圍格子
                    ) {
                        visited.add(newPosition);
                        const newDistance = distance + 1;
                        queue.push({ position: newPosition, distance: newDistance });
                        reachableCells.set(newPosition, newDistance);
                    }
                }
            });
        }
    }

    // 檢查敵人周圍的格子是否可以到達
    const finalReachableCells = new Set(reachableCells.keys());
    enemyAdjacentCells.forEach(cell => {
        const cellRow = Math.floor(cell / controlLayer[currentLevel].size.cols);
        const cellCol = cell % controlLayer[currentLevel].size.cols;

        // 檢查這個格子是否合法（不是障礙物、沒有敵人）
        if (!gameobstacles.includes(cell) &&
            !gameenemys.some(enemy => enemy.Position === cell && enemy.CurHP > 0)) {

            // 檢查是否有相鄰的可到達格子，並找出最短距離
            let minDistance = Infinity;
            directions.forEach(dir => {
                const newRow = cellRow + dir.y;
                const newCol = cellCol + dir.x;
                if (newRow >= 0 && newRow < controlLayer[currentLevel].size.rows &&
                    newCol >= 0 && newCol < controlLayer[currentLevel].size.cols) {
                    const adjacentPos = newRow * controlLayer[currentLevel].size.cols + newCol;
                    if (reachableCells.has(adjacentPos)) {
                        const distance = reachableCells.get(adjacentPos);
                        minDistance = Math.min(minDistance, distance + 1);
                    }
                }
            });

            // 如果最短距離在移動範圍內，且沒有玩家站在這個位置，則添加到可移動範圍
            if (minDistance <= moveRange) {
                const hasPlayer = gameplayers.some(player => player.Position === cell);
                if (!hasPlayer) {
                    finalReachableCells.add(cell);
                }
            }
        }
    });

    return finalReachableCells;
}

// 找出最弱的敵人（僅按血量）
function findWeakestEnemy(enemys) {
    return enemys.reduce((weakest, current) => {
        if (current.CurHP <= 0) return weakest;
        if (!weakest || current.CurHP < weakest.CurHP) return current;
        return weakest;
    }, null);
}

// 找出最弱的敵人（按戰力：防禦+生命）
function findWeakestEnemyByPower(enemys) {
    return enemys.reduce((weakest, current) => {
        if (current.CurHP <= 0) return weakest;
        if (!weakest) return current;

        const currentPower = current.CurHP + current.DEF;
        const weakestPower = weakest.CurHP + weakest.DEF;

        return currentPower < weakestPower ? current : weakest;
    }, null);
}

// 等待函數
function wait(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

// 找出最近的敵人
function findClosestEnemy(playerPosition, enemys) {
    return enemys.reduce((closest, current) => {
        if (current.CurHP <= 0) return closest;
        if (!closest) return current;
        let currentDistance = calculateDistance(playerPosition, current.Position);
        let closestDistance = calculateDistance(playerPosition, closest.Position);
        return currentDistance < closestDistance ? current : closest;
    }, null);
}

// 尋找最佳攻擊位置
function findBestAttackPosition(player, moveRange, enemys) {
    let positions = [];
    let bestTarget = null;

    // 首先找出所有可以攻擊到敵人的位置
    for (let movePos of moveRange) {
        let tempPlayer = { ...player, Position: movePos };

        for (let enemy of enemys) {
            if (enemy.CurHP <= 0) continue;

            if (isEnemyInRange(tempPlayer, enemy)) {
                let distance = calculateDistance(movePos, enemy.Position);
                positions.push({
                    position: movePos,
                    target: enemy,
                    distance: distance
                });
            }
        }
    }

    // 按距離排序
    positions.sort((a, b) => a.distance - b.distance);

    // 找出第一個未被隊友占據的位置
    for (let pos of positions) {
        const isOccupied = gameplayers.some(teammate =>
            teammate.Position === pos.position && teammate.CurHP > 0
        );
        if (!isOccupied) {
            return { position: pos.position, target: pos.target };
        }
    }

    return { position: null, target: null };
}

// 找出到敵人的最近位置
function findClosestPositionToEnemy(moveRange, enemyPosition) {
    let positions = [];

    // 收集所有可能的移動位置及其距離
    for (let movePos of moveRange) {
        let distance = calculateDistance(movePos, enemyPosition);
        positions.push({
            position: movePos,
            distance: distance
        });
    }

    // 按距離排序
    positions.sort((a, b) => a.distance - b.distance);

    // 找出第一個未被隊友占據的位置
    for (let pos of positions) {
        const isOccupied = gameplayers.some(teammate =>
            teammate.Position === pos.position && teammate.CurHP > 0
        );
        if (!isOccupied) {
            return pos.position;
        }
    }

    return closestPosition;
}

// 計算兩個位置之間的距離
function calculateDistance(pos1, pos2) {
    let x1 = Math.floor(pos1 / controlLayer[currentLevel].size.cols);
    let y1 = pos1 % controlLayer[currentLevel].size.cols;
    let x2 = Math.floor(pos2 / controlLayer[currentLevel].size.cols);
    let y2 = pos2 % controlLayer[currentLevel].size.cols;
    return Math.abs(x1 - x2) + Math.abs(y1 - y2);
}

// 檢查敵人是否在攻擊範圍內
function isEnemyInRange(player, enemy) {
    if (!enemy || enemy.CurHP <= 0) return false;
    let distance = calculateDistance(player.Position, enemy.Position);
    return distance <= player.ATKRange;
}

// 尋找玩家移動路徑
function findPlayerPath(startPosition, targetPosition, moveRange, playerIndex) {
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    const queue = [{ position: startPosition, distance: 0, path: [startPosition] }];
    const visited = new Set();
    visited.add(startPosition);

    while (queue.length > 0) {
        const { position, distance, path } = queue.shift();

        if (position === targetPosition) {
            return path;
        }

        if (distance < moveRange) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    if (
                        !visited.has(newPosition) &&
                        !gameobstacles.includes(newPosition) &&
                        !gameenemys.some(enemy => enemy.Position === newPosition && enemy.CurHP > 0)
                    ) {
                        visited.add(newPosition);
                        queue.push({ position: newPosition, distance: distance + 1, path: [...path, newPosition] });
                    }
                }
            });
        }
    }

    return [];
}

// AI玩家移動動畫 - Canvas版本，完全參考players.js的animatePlayerMove
function animateAIPlayerMove(path, playerIndex, setAlreadyMove = true) {
    if (path.length === 0) {
        console.warn("AI玩家移動路徑為空，無法進行移動動畫");
        return;
    }

    console.log(`開始AI玩家 ${playerIndex} 移動動畫，路徑長度: ${path.length}`);

    let currentPathIndex = 0;
    let player = gameplayers[playerIndex];
    if (player.CurHP <= 0) {
        console.warn(`AI玩家 ${playerIndex} 已死亡，取消移動動畫`);
        return;
    }

    let stepaudio = operates.playSound("./Public/footstepsound.mp3", { loop: true });
    if (!stepaudio) {
        console.warn("播放AI玩家移動音效失敗");
    }

    // 停止AI玩家的站立動畫
    let playerObj = mapObjects.find(obj =>
        obj.type === 'player' && obj.playerIndex === playerIndex
    );
    if (playerObj) {
        stopStandAnimation(playerObj);
    }

    // 動畫參數（與玩家移動保持一致）
    const moveSpeed = 380; // 每步移動時間(毫秒)
    const frameRate = 16; // 約60fps

    function animateStep() {
        // 檢查是否到達路徑終點
        if (currentPathIndex >= path.length - 1) {
            // AI玩家到達目的地
            let finalPos = path[currentPathIndex];
            player.Position = finalPos;

            // 只有在純移動時才設置AlreadyMove，攻擊時會在攻擊完成後設置
            if (setAlreadyMove) {
                player.AlreadyMove = true;
                console.log(`AI玩家 ${player.name} 純移動完成，設置為已行動 (AlreadyMove = true)`);
            }

            // 停止移動音效（如果存在）
            if (stepaudio) {
                stepaudio.pause();
            }

            clearAllHighlights();

            // 移動完成後使用 Stand 圖片
            setAIPlayerStandImage(player, playerIndex, finalPos);

            updateMapObjects(); // 重新更新所有物件到最終位置
            render(); // 重新渲染

            // 重新啟動站立動畫
            let playerObj = mapObjects.find(obj =>
                obj.type === 'player' && obj.playerIndex === playerIndex
            );
            if (playerObj) {
                resumeStandAnimation(playerObj);
            }

            console.log(`AI玩家 ${playerIndex} 移動完成，到達位置 ${finalPos}`);
            return;
        }

        let currentPos = path[currentPathIndex];
        let nextPos = path[currentPathIndex + 1];

        // 計算當前和下一個位置的行列座標
        let rowCurrent = Math.floor(currentPos / controlLayer[currentLevel].size.cols);
        let colCurrent = currentPos % controlLayer[currentLevel].size.cols;
        let rowNext = Math.floor(nextPos / controlLayer[currentLevel].size.cols);
        let colNext = nextPos % controlLayer[currentLevel].size.cols;

        // 獲取移動方向
        let directionIndex = getAIPlayerDirection(rowCurrent, colCurrent, rowNext, colNext);
        let directionName = getAIPlayerDirectionName(directionIndex);

        // 驗證方向和MoveRes
        if (directionIndex < 0 || !directionName || !player.MoveRes[directionName]) {
            console.error(`無效的AI玩家移動方向: ${directionIndex}, ${directionName}`);
            console.error("AI玩家MoveRes:", player.MoveRes);
            return;
        }

        // 記錄最後移動的方向
        player.lastdirect = directionName;

        // 開始平滑移動動畫
        smoothMoveAIPlayerToNextCell(
            currentPos,
            nextPos,
            player,
            playerIndex,
            directionName,
            moveSpeed,
            () => {
                // 移動完成後的回調
                currentPathIndex++;
                animateStep(); // 繼續下一步
            }
        );
    }

    // 開始動畫
    animateStep();
}

// AI玩家方向計算函數
function getAIPlayerDirection(fromRow, fromCol, toRow, toCol) {
    if (fromRow < toRow) return 0; // down
    if (fromRow > toRow) return 1; // up
    if (fromCol < toCol) return 2; // right
    if (fromCol > toCol) return 3; // left
    return -1; // 無效方向
}

function getAIPlayerDirectionName(directionIndex) {
    const directions = ['down', 'up', 'right', 'left'];
    return directions[directionIndex] || null;
}

// 設置AI玩家站立圖片
function setAIPlayerStandImage(player, playerIndex, position) {
    // 使用記錄的最後移動方向
    let lastDirection = player.lastdirect || 'down';

    console.log(`AI玩家 ${player.name} 最後移動方向: ${lastDirection}`);

    // 獲取對應方向的站立圖片
    let standImages = player.Stand && player.Stand[lastDirection];
    if (!standImages || !Array.isArray(standImages) || standImages.length === 0) {
        // 如果沒有對應方向的站立圖片，使用預設圖片
        console.warn(`AI玩家 ${player.name} 沒有 ${lastDirection} 方向的站立圖片，使用 down 方向`);
        standImages = player.Stand && player.Stand['down'];
        if (!standImages) {
            console.warn(`AI玩家 ${player.name} 沒有站立圖片`);
            return;
        }
    }

    // 找到AI玩家物件並更新其站立圖片陣列
    let playerObj = mapObjects.find(obj =>
        obj.type === 'player' && obj.playerIndex === playerIndex
    );

    if (playerObj) {
        // 更新站立圖片陣列為新方向
        playerObj.standImages = standImages;
        playerObj.currentFrameIndex = 0;

        // 使用第一張站立圖片
        let standImageSrc = standImages[0];

        console.log(`設置AI玩家 ${player.name} 站立圖片: ${standImageSrc}`);

        // 更新AI玩家物件的圖片和位置
        updateAIPlayerPositionInMapObjects(playerIndex, position, standImageSrc);
    }
}

// 平滑移動AI玩家到下一個格子的函數 - 參考players.js的實現
function smoothMoveAIPlayerToNextCell(fromPos, toPos, player, playerIndex, direction, duration, onComplete) {
    const startTime = performance.now();

    // 計算起始和目標座標
    const fromCol = fromPos % controlLayer[currentLevel].size.cols;
    const fromRow = Math.floor(fromPos / controlLayer[currentLevel].size.cols);
    const toCol = toPos % controlLayer[currentLevel].size.cols;
    const toRow = Math.floor(toPos / controlLayer[currentLevel].size.cols);

    // 計算像素座標
    const startX = fromCol * cellWidth;
    const startY = fromRow * cellHeight;
    const endX = toCol * cellWidth;
    const endY = toRow * cellHeight;

    // 獲取移動圖片
    let moveImages = player.MoveRes[direction];

    function animate(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1); // 0 到 1 的進度

        // 使用緩動函數讓移動更自然
        const easeProgress = easeInOutQuadAIPlayer(progress);

        // 計算當前位置
        const currentX = startX + (endX - startX) * easeProgress;
        const currentY = startY + (endY - startY) * easeProgress;

        // 選擇當前動畫幀
        let currentImage;
        if (Array.isArray(moveImages)) {
            // 根據時間循環播放動畫幀
            const frameIndex = Math.floor((elapsed / 100)) % moveImages.length; // 每100ms換一幀
            currentImage = moveImages[frameIndex];
        } else if (typeof moveImages === 'string') {
            currentImage = moveImages;
        }

        // 更新AI玩家在mapObjects中的位置和圖片
        updateAIPlayerSmoothPosition(playerIndex, currentX, currentY, currentImage);

        // 重新渲染
        render();

        // 檢查動畫是否完成
        if (progress < 1) {
            requestAnimationFrame(animate);
        } else {
            // 動畫完成，確保AI玩家位置精確
            updateAIPlayerSmoothPosition(playerIndex, endX, endY, currentImage);
            render();
            onComplete();
        }
    }

    requestAnimationFrame(animate);
}

// 緩動函數 - 讓移動更自然
function easeInOutQuadAIPlayer(t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
}

// 更新AI玩家平滑移動位置的函數
function updateAIPlayerSmoothPosition(playerIndex, pixelX, pixelY, imageSrc) {
    // 找到對應的AI玩家物件
    let playerObj = mapObjects.find(obj =>
        obj.type === 'player' && obj.playerIndex === playerIndex
    );

    if (!playerObj) {
        console.warn(`找不到AI玩家 ${playerIndex} 的物件`);
        return;
    }

    // 載入新圖片
    if (imageSrc) {
        preloadImage(imageSrc).then(img => {
            playerObj.img = img;
        }).catch(error => {
            console.warn(`載入AI玩家移動圖片失敗: ${imageSrc}`, error);
        });
    }

    // 設置自定義位置
    playerObj.useCustomPosition = true;
    playerObj.customX = pixelX;
    playerObj.customY = pixelY;
}

// 更新AI玩家位置在mapObjects中的函數
function updateAIPlayerPositionInMapObjects(playerIndex, position, imageSrc) {
    let playerObj = mapObjects.find(obj =>
        obj.type === 'player' && obj.playerIndex === playerIndex
    );

    if (playerObj) {
        // 更新格子位置
        playerObj.gridX = position % controlLayer[currentLevel].size.cols;
        playerObj.gridY = Math.floor(position / controlLayer[currentLevel].size.cols);
        playerObj.useCustomPosition = false;

        // 載入新圖片
        if (imageSrc) {
            preloadImage(imageSrc).then(img => {
                playerObj.img = img;
                render();
            }).catch(error => {
                console.warn(`載入AI玩家圖片失敗: ${imageSrc}`, error);
            });
        }
    }
}

// 停止站立動畫
function stopStandAnimation(playerObj) {
    if (playerObj && playerObj.isStandAnimating) {
        playerObj.isStandAnimating = false;
        if (playerObj.animationId) {
            cancelAnimationFrame(playerObj.animationId);
            playerObj.animationId = null;
        }
    }
}

// 恢復站立動畫
function resumeStandAnimation(playerObj) {
    if (playerObj && playerObj.standImages && playerObj.standImages.length > 1) {
        // 重置到第一幀
        playerObj.currentFrameIndex = 0;
        startStandAnimation(playerObj);
    }
}

// 設置AI玩家戰鬥時的面向方向 - 參考Enemys.js的setBattleFacingDirection
async function setAIPlayerBattleFacingDirection(player, enemy, playerIndex) {
    console.log(`設置AI玩家戰鬥面向：AI玩家 ${player.name} 與敵人 ${enemy.name}`);

    // 計算AI玩家和敵人的位置
    const playerCol = player.Position % controlLayer[currentLevel].size.cols;
    const playerRow = Math.floor(player.Position / controlLayer[currentLevel].size.cols);
    const enemyCol = enemy.Position % controlLayer[currentLevel].size.cols;
    const enemyRow = Math.floor(enemy.Position / controlLayer[currentLevel].size.cols);

    // 計算AI玩家應該面向敵人的方向
    const playerDirection = calculateAIPlayerFacingDirection(playerCol, playerRow, enemyCol, enemyRow);
    console.log(`AI玩家 ${player.name} 轉向: ${playerDirection}`);

    // 計算敵人應該面向AI玩家的方向
    const enemyDirection = calculateAIPlayerFacingDirection(enemyCol, enemyRow, playerCol, playerRow);
    console.log(`敵人 ${enemy.name} 轉向: ${enemyDirection}`);

    // 更新AI玩家的面向方向
    player.lastdirect = playerDirection;

    // 更新敵人的面向方向
    enemy.lastdirect = enemyDirection;

    // 更新AI玩家在canvas中的圖片
    await updateAIPlayerFacingImage(player, playerIndex, playerDirection);

    // 更新敵人在canvas中的圖片
    await updateEnemyFacingImageForAI(enemy, enemyDirection);

    // 重新渲染
    render();

    console.log(`AI玩家戰鬥面向設置完成：AI玩家面向${playerDirection}，敵人面向${enemyDirection}`);
}

// 計算AI玩家面向方向
function calculateAIPlayerFacingDirection(fromCol, fromRow, toCol, toRow) {
    const deltaCol = toCol - fromCol;
    const deltaRow = toRow - fromRow;

    // 優先考慮主要方向（水平或垂直）
    if (Math.abs(deltaCol) > Math.abs(deltaRow)) {
        // 水平方向為主
        return deltaCol > 0 ? 'right' : 'left';
    } else if (Math.abs(deltaRow) > Math.abs(deltaCol)) {
        // 垂直方向為主
        return deltaRow > 0 ? 'down' : 'up';
    } else {
        // 對角線情況，選擇垂直方向
        return deltaRow > 0 ? 'down' : 'up';
    }
}

// 更新AI玩家面向圖片
async function updateAIPlayerFacingImage(player, playerIndex, direction) {
    try {
        console.log(`更新AI玩家 ${player.name} 面向圖片: ${direction}`);

        // 獲取對應方向的站立圖片
        let standImages = player.Stand && player.Stand[direction];
        if (!standImages || !Array.isArray(standImages) || standImages.length === 0) {
            console.warn(`AI玩家 ${player.name} 沒有 ${direction} 方向的站立圖片，使用down方向`);
            standImages = player.Stand && player.Stand['down'];
            if (!standImages) {
                console.warn(`AI玩家 ${player.name} 沒有站立圖片`);
                return;
            }
        }

        // 找到AI玩家物件
        let playerObj = mapObjects.find(obj =>
            obj.type === 'player' && obj.playerIndex === playerIndex
        );

        if (playerObj) {
            // 載入新的站立圖片
            const standImageSrc = standImages[0];
            const standImg = await preloadImage(standImageSrc);

            // 更新AI玩家物件
            playerObj.img = standImg;
            playerObj.standImages = standImages;
            playerObj.currentFrameIndex = 0;

            console.log(`AI玩家 ${player.name} 圖片已更新為 ${direction} 方向`);
        } else {
            console.warn(`找不到AI玩家 ${playerIndex} 的canvas物件`);
        }
    } catch (error) {
        console.error(`更新AI玩家面向圖片時發生錯誤:`, error);
    }
}

// 更新敵人面向圖片（為AI玩家攻擊時使用）
async function updateEnemyFacingImageForAI(enemy, direction) {
    try {
        console.log(`更新敵人 ${enemy.name} 面向圖片: ${direction}`);

        // 獲取敵人索引
        const enemyIndex = gameenemys.findIndex(e => e === enemy);
        if (enemyIndex === -1) {
            console.error("找不到敵人索引");
            return;
        }

        // 獲取對應方向的站立圖片
        let standImages = enemy.Stand && enemy.Stand[direction];
        if (!standImages || !Array.isArray(standImages) || standImages.length === 0) {
            console.warn(`敵人 ${enemy.name} 沒有 ${direction} 方向的站立圖片，使用down方向`);
            standImages = enemy.Stand && enemy.Stand['down'];
            if (!standImages) {
                console.warn(`敵人 ${enemy.name} 沒有站立圖片`);
                return;
            }
        }

        // 找到敵人物件
        let enemyObj = mapObjects.find(obj =>
            obj.type === 'enemy' && obj.enemyIndex === enemyIndex
        );

        if (enemyObj) {
            // 載入新的站立圖片
            const standImageSrc = standImages[0];
            const standImg = await preloadImage(standImageSrc);

            // 更新敵人物件
            enemyObj.img = standImg;
            enemyObj.standImages = standImages;
            enemyObj.currentFrameIndex = 0;

            console.log(`敵人 ${enemy.name} 圖片已更新為 ${direction} 方向`);
        } else {
            console.warn(`找不到敵人 ${enemyIndex} 的canvas物件`);
        }
    } catch (error) {
        console.error(`更新敵人面向圖片時發生錯誤:`, error);
    }
}