#magicdialog {
    width: 100%;
    height: 100%;
    background: none;
    outline: none;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

#magicdialog::backdrop {
    background-color: rgba(0, 0, 0, 0.9);
}

#magicinfo {
    width: 750px;
    height: 100px;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url(../Public/magicinfo.png);
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}


#outer-magic {
    width: 1000px;
    height: 650px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20px;
    animation: magicAni 1s forwards ease-in-out;
}

#magiccontain {
    width: 900px;
    height: 450px;
    background-image: url(../Public/magicwin.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transition: 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;
}

#magiccontent {
    width: 95%;
    height: 82%;
    display: flex;
    justify-content: start;
    align-items: start;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 5px;
}


#magicinfotext {
    width: 90%;
    height: 85%;
    font-size: 24px;
    letter-spacing: 2px;
    font-weight: 600;
    color: rgb(168, 105, 38);
    display: flex;
    justify-items: start;
    align-items: start;
}

#magicclosebtn {
    position: absolute;
    top: 5%;
    left: 3%;
    font-size: 50px;
    background: rgb(247, 231, 173);
    border: 4px solid rgb(165, 90, 24);
    font-weight: 600;
    color: rgb(168, 105, 38);
    width: 5%;
    cursor: pointer;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.usemagicitem {
    padding: 0;
    margin: 0;
    width: 40%;
    height: 17%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    letter-spacing: 2px;
    font-size: 30px;
    white-space: nowrap;
    font-weight: 600;
    cursor: pointer;
    color: rgb(168, 105, 38);
}

.usemagicitem:hover {
    border: 3px solid brown;
}

.usemagicitem_name {
    width: 50%;
}

.usemagicitem_consume {
    width: 20%;
    font-size: 18px;
}

.consumeitem {
    display: flex;
    white-space: nowrap;
}

#magicnamedialog {
    width: 100%;
    height: 100%;
    background: none;
    outline: none;
    border: none;
    display: flex;
    align-items: start;
    justify-content: center;
}

#magicnamedialog::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

#magicname {
    margin-top: 5%;
    width: 20%;
    height: 12%;
    font-size: 35px;
    font-weight: 600;
    color: rgb(165, 90, 24);
    text-align: center;
    letter-spacing: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url(../Public/showskillbg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    animation: magicupappear 1s forwards ease-in-out;
}

@keyframes magicAni {
    from {
        opacity: 0;
        transform: translateY(50%);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes magicupappear {
    from {
        opacity: 0;
        transform: translateY(-50%);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}