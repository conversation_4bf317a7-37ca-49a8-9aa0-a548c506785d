// 武器資料庫
const Weapons = {
    "長劍": {
        type: "weapon",
        Who: ["殷劍平"],
        name: "長劍",
        ATK: 24,
        description: "攻擊力+24",
        price: 10,
        image: "./Public/Items/weapons/weapon0.png",
        sytles: "normal-sword-icon"
    },
    "匕首": {
        type: "weapon",
        Who: ["封寒月"],
        name: "匕首",
        ATK: 20,
        description: "攻擊力+20",
        price: 10,
        image: "./Public/Items/weapons/weapon4.png",
        sytles: "normal2-sword-icon"
    },
    "青鋼劍": {
        type: "weapon",
        Who: ["殷劍平"],
        name: "青鋼劍",
        ATK: 40,
        description: "攻擊力+40",
        price: 200,
        image: "./Public/Items/weapons/weapon1.png",
        sytles: "normal2-sword-icon"
    },
    "紫金劍": {
        type: "weapon",
        Who: ["殷劍平"],
        name: "紫金劍",
        ATK: 110,
        description: "攻擊力+110",
        price: 700,
        image: "./Public/Items/weapons/weapon2.png",
        sytles: "purple-sword-icon"

    },
    "古劍‧龍形": {
        type: "weapon",
        Who: ["殷劍平"],
        name: "古劍‧龍形",
        ATK: 120,
        description: "攻擊力+120",
        price: 800,
        image: "./Public/Items/weapons/weapon3.png",
        sytles: "gold-sword-icon"
    }
};

// 防具資料庫
const Armors = {
    "布衣": {
        type: "armor",
        name: "布衣",
        Who: ["殷劍平", "封寒月"],
        DEF: 8,
        description: "防禦力+8",
        price: 10,
        image: "./Public/Items/armors/armor0.png",
        sytles: "normal-armor-icon"
    },
    "法袍": {
        type: "armor",
        Who: ["封寒月"],
        name: "法袍",
        DEF: 13,
        description: "防禦力+13",
        price: 10,
        image: "./Public/Items/armors/armor4.png",
        sytles: "normal-armor-icon"
    },
    "軟革裏衣": {
        type: "armor",
        Who: ["殷劍平"],
        name: "軟革裏衣",
        DEF: 15,
        description: "防禦力+15",
        price: 60,
        image: "./Public/Items/armors/armor1.png",
        sytles: "normal-sword-icon"
    },
    "硬皮胸甲": {
        type: "armor",
        Who: ["殷劍平"],
        name: "軟革裏衣",
        DEF: 30,
        description: "防禦力+30",
        price: 100,
        image: "./Public/Items/armors/armor5.png",
        sytles: "normal-sword-icon"
    },
    "蟒神甲": {
        type: "armor",
        Who: ["殷劍平"],
        name: "蟒神甲",
        DEF: 280,
        description: "防禦力+280",
        price: 2000,
        image: "./Public/Items/armors/armor2.png",
        sytles: "brown-armor-icon"
    },
    "龍王鎧": {
        type: "armor",
        Who: ["殷劍平"],
        name: "龍王鎧",
        DEF: 500,
        description: "防禦力+500",
        price: 5500,
        image: "./Public/Items/armors/armor3.png",
        sytles: "green-armor-icon"
    }
};

// 飾品資料庫
const Fittings = {
    "避邪玉珮": {
        type: "accessory",
        Who: ["殷劍平", "封寒月"],
        name: "避邪玉珮",
        effect: {
            MP: 0,
            DEF: 5
        },
        description: "防禦力+5",
        price: 10,
        image: "./Public/Items/fitting/fitting0.png",
        sytles: "normal-fitting-icon"
    },
    "佛珠": {
        type: "accessory",
        Who: ["殷劍平", "封寒月"],
        name: "佛珠",
        effect: {
            MP: 0,
            DEF: 5
        },
        description: "防禦力+5",
        price: 10,
        image: "./Public/Items/fitting/fitting4.png",
        sytles: "normal-fitting-icon"
    },
    "妖磷石": {
        type: "accessory",
        Who: ["殷劍平", "封寒月"],
        name: "妖磷石",
        effect: {
            MP: 5,
            DEF: 70
        },
        description: "法力+5 防禦力+70",
        price: 1500,
        image: "./Public/Items/fitting/fitting1.png",
        sytles: "demon-fitting-icon"
    },
    "雲龍珠": {
        type: "accessory",
        Who: ["殷劍平", "封寒月"],
        name: "雲龍珠",
        effect: {
            MP: 50,
            DEF: 150
        },
        description: "法力+50 防禦力+150",
        price: 3000,
        image: "./Public/Items/fitting/fitting2.png",
        sytles: "clouded-ring-icon"
    },
    "兩儀玄石": {
        type: "accessory",
        Who: ["殷劍平", "封寒月"],
        name: "兩儀玄石",
        effect: {
            MP: 220,
            DEF: 300
        },
        description: "法力+220 防禦力+300",
        price: 6000,
        image: "./Public/Items/fitting/fitting3.png",
        sytles: "Liangyi-fitting-icon"
    },
    "燧石": {
        type: "accessory",
        Who: ["殷劍平", "封寒月"],
        name: "燧石",
        effect: {
            MP: 10,
            DEF: -10
        },
        description: "法力+10 防禦力-10",
        price: 99999,
        image: "./Public/Items/materials/material0.png",
        sytles: "material-fire-icon"
    }
};

// 食物藥品資料庫
const Eatting = {
    "金創藥": {
        type: "medicine",
        name: "金創藥",
        effect: {
            HP: 0,
            MP: 0,
            ATK: 0,
            DEF: 0,
            AvoidRate: 0,
            curHP: 250,
            curMP: 0
        },
        description: "回復自身250點以上 HP",
        price: 50,
        image: "./Public/Items/eatting/eatting0.png",
        animates: ["./Public/maneff/recover/0.", "./Public/maneff/recover/1.png", "./Public/maneff/recover/2.png", "./Public/maneff/recover/3.png", "./Public/maneff/recover/4.png", "./Public/maneff/recover/5.png", "./Public/maneff/recover/6.png", "./Public/maneff/recover/7.png", "./Public/maneff/recover/8.png", "./Public/maneff/recover/9.png", "./Public/maneff/recover/10.png", "./Public/maneff/recover/11.png", "./Public/maneff/recover/12.png"],
        sound: "./Public/maneff/recover/recover.mp3",
        sytles: "Eating-normal-icon",
        range: 0
    },
    "返氣丸": {
        type: "medicine",
        name: "返氣丸",
        effect: {
            HP: 0,
            MP: 0,
            ATK: 0,
            DEF: 0,
            AvoidRate: 0,
            curHP: 0,
            curMP: 200
        },
        description: "回復自身200點以上 法力",
        price: 3000,
        image: "./Public/Items/eatting/eatting1.png",
        animates: ["./Public/maneff/recover/0.", "./Public/maneff/recover/1.png", "./Public/maneff/recover/2.png", "./Public/maneff/recover/3.png", "./Public/maneff/recover/4.png", "./Public/maneff/recover/5.png", "./Public/maneff/recover/6.png", "./Public/maneff/recover/7.png", "./Public/maneff/recover/8.png", "./Public/maneff/recover/9.png", "./Public/maneff/recover/10.png", "./Public/maneff/recover/11.png", "./Public/maneff/recover/12.png"],
        sound: "./Public/maneff/recover/recover.mp3",
        sytles: "Eating-normal-icon",
        range: 0
    },
    "大補丸": {
        type: "medicine",
        name: "大補丸",
        effect: {
            HP: 0,
            MP: 0,
            ATK: 0,
            DEF: 0,
            AvoidRate: 0,
            curHP: 600,
            curMP: 0
        },
        description: "回復自身600點以上 HP",
        price: 2200,
        image: "./Public/Items/eatting/eatting1.png",
        animates: ["./Public/maneff/recover/0.", "./Public/maneff/recover/1.png", "./Public/maneff/recover/2.png", "./Public/maneff/recover/3.png", "./Public/maneff/recover/4.png", "./Public/maneff/recover/5.png", "./Public/maneff/recover/6.png", "./Public/maneff/recover/7.png", "./Public/maneff/recover/8.png", "./Public/maneff/recover/9.png", "./Public/maneff/recover/10.png", "./Public/maneff/recover/11.png", "./Public/maneff/recover/12.png"],
        sound: "./Public/maneff/recover/recover.mp3",
        sytles: "Eating-normal-icon",
        range: 0
    }
};

// 將物品資料設置為全局變量
window.Weapons = Weapons;
window.Armors = Armors;
window.Fittings = Fittings;
window.Eatting = Eatting; 