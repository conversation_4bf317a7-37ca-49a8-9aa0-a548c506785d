var MagicDB = [
    {
        name: "氣愈之術",
        type: "法術",
        Classify: "治癒",
        describsion: "最初級的療癒法術，集聚體內之氣於一處，可治癒較輕微的傷勢",
        Who: ["殷劍平"],
        NeedMove: 0,
        NeedLV: 3,
        distance: 4,
        Rmdistance: 0,
        Range: 1,
        NeedMP: 14,
        NeedSoul: {
            "迅": 0,
            "烈": 0,
            "神": 0,
            "魔": 0,
            "魂": 0
        },
        Icon: "./Public/Magic/0/icon.png",
        Animaties: ["./Public/Magic/0/Animation/0.png", "./Public/Magic/0/Animation/1.png", "./Public/Magic/0/Animation/2.png", "./Public/Magic/0/Animation/3.png", "./Public/Magic/0/Animation/4.png", "./Public/Magic/0/Animation/5.png", "./Public/Magic/0/Animation/6.png", "./Public/Magic/0/Animation/7.png", "./Public/Magic/0/Animation/8.png", "./Public/Magic/0/Animation/9.png", "./Public/Magic/0/Animation/10.png", "./Public/Magic/0/Animation/11.png", "./Public/Magic/0/Animation/12.png", "./Public/Magic/0/Animation/13.png", "./Public/Magic/0/Animation/14.png", "./Public/Magic/0/Animation/15.png", "./Public/Magic/0/Animation/16.png", "./Public/Magic/0/Animation/17.png", "./Public/Magic/0/Animation/18.png", "./Public/Magic/0/Animation/19.png", "./Public/Magic/0/Animation/20.png", "./Public/Magic/0/Animation/21.png", "./Public/Magic/0/Animation/22.png", "./Public/Magic/0/Animation/23.png", "./Public/Magic/0/Animation/24.png", "./Public/Magic/0/Animation/25.png", "./Public/Magic/0/Animation/26.png", "./Public/Magic/0/Animation/27.png", "./Public/Magic/0/Animation/28.png", "./Public/Magic/0/Animation/29.png", "./Public/Magic/0/Animation/30.png", "./Public/Magic/0/Animation/31.png"],
        EffAnimates: ["./Public/maneff/recover/0.png", "./Public/maneff/recover/1.png", "./Public/maneff/recover/2.png", "./Public/maneff/recover/3.png", "./Public/maneff/recover/4.png", "./Public/maneff/recover/5.png", "./Public/maneff/recover/6.png", "./Public/maneff/recover/7.png", "./Public/maneff/recover/8.png", "./Public/maneff/recover/9.png", "./Public/maneff/recover/10.png", "./Public/maneff/recover/11.png", "./Public/maneff/recover/12.png"],
        Sounds: "./Public/Magic/0/sound.mp3",
        effect: function (playerMP) {
            const healAmount = Math.floor(playerMP * 0.55 + 60);
            return {
                type: "heal",
                value: healAmount,
                target: "self"
            };
        },
        exp: function calculateHealExp(healAmount, healerLevel, targetLevel) {
            // 等級差影響
            const levelDiff = healerLevel - targetLevel;

            if (levelDiff >= 6) {
                return 1; // 高出太多，只得1點
            }

            // 基礎經驗：補血量 * 40%
            return Math.floor(healAmount * 0.4);
        }
    },
    {
        name: "無方飛劍",
        type: "武功",
        Classify: "傷害",
        describsion: "殷劍平初段的御劍之法，可聚氣於劍尖遠射攻敵，威力不弱",
        Who: ["殷劍平"],
        NeedMove: 2.4,
        NeedLV: 15,
        distance: 5,
        Rmdistance: 4,
        Range: 0,
        NeedMP: 20,
        NeedSoul: {
            "迅": 15,
            "烈": 25,
            "神": 25,
            "魔": 25,
            "魂": 25
        },
        Icon: "./Public/Magic/1/icon.png",
        Animations: "./Public/Magic/1/",
        Sounds: ".Public/Magic/1/1.mp3",
        effect: function (playeratk) {
            const damageAmount = Math.floor(playeratk * 1.55 + 60);
            return {
                type: "damage",
                value: damageAmount,
                target: "self"
            };
        }
    },
    {
        name: "離火神訣",
        type: "法術",
        Classify: "火系",
        describsion: "引離火之氣包於敵人身邊，可焚燒小群敵人，造成一般程度的創傷",
        Who: ["封寒月"],
        NeedMove: 0,
        NeedLV: 3,
        distance: 4,
        Rmdistance: 0,
        Range: 1,
        NeedMP: 10,
        NeedSoul: {
            "迅": 0,
            "烈": 0,
            "神": 0,
            "魔": 0,
            "魂": 0
        },
        Icon: "./Public/Magic/2/2.png",
        Animaties: ["./Public/Magic/2/Animation/0.png", "./Public/Magic/2/Animation/1.png", "./Public/Magic/2/Animation/2.png", "./Public/Magic/2/Animation/3.png", "./Public/Magic/2/Animation/4.png", "./Public/Magic/2/Animation/5.png", "./Public/Magic/2/Animation/6.png", "./Public/Magic/2/Animation/7.png", "./Public/Magic/2/Animation/8.png", "./Public/Magic/2/Animation/9.png", "./Public/Magic/2/Animation/10.png", "./Public/Magic/2/Animation/11.png", "./Public/Magic/2/Animation/12.png", "./Public/Magic/2/Animation/13.png", "./Public/Magic/2/Animation/14.png", "./Public/Magic/2/Animation/15.png", "./Public/Magic/2/Animation/16.png", "./Public/Magic/2/Animation/17.png", "./Public/Magic/2/Animation/18.png", "./Public/Magic/2/Animation/19.png", "./Public/Magic/2/Animation/20.png", "./Public/Magic/2/Animation/21.png", "./Public/Magic/2/Animation/22.png", "./Public/Magic/2/Animation/23.png", "./Public/Magic/2/Animation/24.png", "./Public/Magic/2/Animation/25.png", "./Public/Magic/2/Animation/26.png", "./Public/Magic/2/Animation/27.png", "./Public/Magic/2/Animation/28.png"],
        EffAnimates: ["./Public/maneff/damage/0.png", "./Public/maneff/damage/1.png", "./Public/maneff/damage/2.png", "./Public/maneff/damage/3.png", "./Public/maneff/damage/4.png", "./Public/maneff/damage/5.png", "./Public/maneff/damage/6.png", "./Public/maneff/damage/7.png"],
        Sounds: "./Public/Magic/2/sound.mp3",
        effect: function (playerMP) {
            const damageamount = Math.floor(playerMP * 0.45 + 50);
            return {
                type: "fire",
                value: damageamount,
                target: "self"
            };
        },
        exp: function getExpFromRemainingHp(remainingHp, maxHp) {
            const remainingRatio = remainingHp / maxHp;
            const maxExp = 70;
            const exp = 70 + Math.floor(maxExp * (1 - remainingRatio));
            return exp;
        }
    },
]